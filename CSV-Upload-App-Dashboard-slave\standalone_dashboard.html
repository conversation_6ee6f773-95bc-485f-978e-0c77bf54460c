<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="https://public-frontend-cos.metadl.com/mgx/img/favicon.png" type="image/png">
    <title>Transaction Data Dashboard - Standalone</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f7f9fc;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }

        .upload-section {
            background-color: #ffffff;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
            margin-bottom: 30px;
        }

        input[type="file"] {
            display: block;
            margin: 20px auto;
            padding: 10px;
            width: 100%;
            max-width: 400px;
        }

        button {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        button:hover {
            background-color: #2980b9;
        }

        .loading-indicator {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-left: 4px solid #3498db;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin-bottom: 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .progress-container {
            width: 100%;
            max-width: 500px;
            margin: 20px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e0e0e0;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 10px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            text-align: center;
            margin-top: 10px;
            font-weight: 500;
            color: #34495e;
        }

        .memory-info {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-size: 12px;
            color: #856404;
        }

        .hidden {
            display: none;
        }

        .error-message {
            background-color: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            text-align: center;
        }

        .dashboard-container {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        .metrics-row {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 30px;
        }

        .metric-card {
            flex: 1 1 250px;
            background-color: #f1f8fe;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
            text-align: center;
        }

        .clickable-card {
            cursor: pointer;
            transition: background-color 0.2s ease, transform 0.1s ease;
        }

        .clickable-card:hover {
            background-color: #e8f4fd;
            transform: translateY(-1px);
        }

        .metric-card h3 {
            margin-top: 0;
            color: #7f8c8d;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .metric-value {
            font-size: 28px;
            font-weight: bold;
            color: #2c3e50;
        }

        .toggle-icon-small {
            font-size: 12px;
            color: #7f8c8d;
            margin-left: 5px;
            transition: transform 0.3s ease;
        }

        .toggle-icon-small.expanded {
            transform: rotate(180deg);
        }

        .clickable-stat-item {
            cursor: pointer;
            transition: background-color 0.2s ease;
            border-radius: 4px;
            padding: 8px;
            margin: -8px;
        }

        .clickable-stat-item:hover {
            background-color: #f8f9fa;
        }

        .file-summary-section {
            margin-bottom: 30px;
        }

        .files-list {
            background-color: #f8f9fa;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
        }

        .file-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }

        .file-item:last-child {
            border-bottom: none;
        }

        .file-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .file-stats {
            font-size: 14px;
            color: #7f8c8d;
        }

        /* Transaction Summary CSS styles removed as requested */

        .serial-stats-section {
            margin-top: 20px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
        }

        .serial-stats-section h4 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #34495e;
            font-size: 16px;
        }

        .serial-stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 12px;
        }

        .serial-stat-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 12px;
            background-color: #fff5f5;
            border-radius: 4px;
            border-left: 4px solid #e74c3c;
        }

        .serial-label {
            font-weight: 500;
            color: #34495e;
            font-size: 14px;
        }

        .serial-value {
            font-weight: bold;
            color: #c0392b;
            font-size: 16px;
        }

        .data-preview {
            margin-top: 30px;
        }

        .data-preview h3 {
            color: #34495e;
            margin-bottom: 15px;
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            border-spacing: 0;
            font-size: 14px;
        }

        thead {
            background-color: #f1f8fe;
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        th {
            color: #34495e;
            font-weight: 600;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        /* Currency Totals Expandable Component Styles */
        .currency-totals-content {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 1px 5px rgba(0, 0, 0, 0.05);
            margin-bottom: 30px;
            overflow: hidden;
            transition: max-height 0.3s ease, opacity 0.3s ease;
        }

        .currency-totals-content.hidden {
            max-height: 0;
            opacity: 0;
            margin-bottom: 0;
        }

        .currency-totals-content:not(.hidden) {
            max-height: 500px;
            opacity: 1;
        }

        .currency-totals-table-container {
            padding: 0;
        }

        .currency-totals-table {
            width: 100%;
            border-collapse: collapse;
            border-spacing: 0;
            font-size: 14px;
            margin: 0;
        }

        .currency-totals-table thead {
            background-color: #f8f9fa;
        }

        .currency-totals-table th {
            padding: 12px 20px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
            color: #34495e;
            font-weight: 600;
            font-size: 13px;
            text-transform: uppercase;
        }

        .currency-totals-table td {
            padding: 12px 20px;
            text-align: left;
            border-bottom: 1px solid #f1f1f1;
            color: #2c3e50;
        }

        .currency-totals-table tbody tr:hover {
            background-color: #f8f9fa;
        }

        .currency-totals-table .currency-code {
            font-weight: 600;
            color: #3498db;
        }

        .currency-totals-table .currency-amount {
            font-weight: 500;
            text-align: right;
        }

        .currency-totals-table tbody tr:last-child td {
            border-bottom: none;
        }

        @media (max-width: 768px) {
            .metrics-row {
                flex-direction: column;
            }
            
            .charts-container {
                flex-direction: column;
            }
            
            .chart-box {
                flex: 1 1 100%;
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>Transaction Data Dashboard</h1>
        
        <div class="upload-section">
            <p>Upload your transaction CSV files to analyze the data (you can select multiple files)</p>
            <input type="file" id="csvFileUpload" accept=".csv" multiple />
            <button id="uploadBtn">Process Data</button>
        </div>
        
        <div id="loadingIndicator" class="loading-indicator hidden">
            <div class="spinner"></div>
            <p id="loadingText">Processing data...</p>
            <div class="progress-container">
                <div class="progress-bar">
                    <div id="progressFill" class="progress-fill"></div>
                </div>
                <div id="progressText" class="progress-text">0%</div>
            </div>
            <div id="memoryInfo" class="memory-info">
                Memory usage: <span id="memoryUsage">0 MB</span> |
                Files processed: <span id="filesProcessed">0</span> |
                Batch: <span id="currentBatch">0</span>
            </div>
        </div>
        
        <div id="errorMessage" class="error-message hidden"></div>
        
        <div id="dashboardContainer" class="dashboard-container hidden">
            
            <div class="metrics-row">
                <div class="metric-card">
                    <h3>Total Files</h3>
                    <div id="totalFiles" class="metric-value">0</div>
                </div>
                <div class="metric-card">
                    <h3>Total MMK</h3>
                    <div id="totalMMK" class="metric-value">0</div>
                </div>
                <div class="metric-card">
                    <h3>Total USD</h3>
                    <div id="totalUSD" class="metric-value">0</div>
                </div>
                <div class="metric-card">
                    <h3>Total Transactions</h3>
                    <div id="totalTransactions" class="metric-value">0</div>
                </div>
                <div class="metric-card clickable-card" onclick="toggleCurrencyTotals()">
                    <h3>Total Amount <span id="currencyToggleIcon" class="toggle-icon-small">▼</span></h3>
                    <div id="totalAmount" class="metric-value">0</div>
                </div>
                <div class="metric-card">
                    <h3>Average Transaction</h3>
                    <div id="avgTransaction" class="metric-value">0</div>
                </div>
            </div>

            <!-- Expandable Currency Totals Component -->
            <div id="currencyTotalsContent" class="currency-totals-content hidden">
                <div class="currency-totals-table-container">
                    <table class="currency-totals-table">
                        <thead>
                            <tr>
                                <th>Currency</th>
                                <th style="text-align: right;">Total Amount</th>
                            </tr>
                        </thead>
                        <tbody id="currencyTotalsTableBody">
                            <!-- Currency rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="serial-stats-section">
                <h4>Serial Number Statistics</h4>
                <div class="serial-stats-grid">
                    <div class="serial-stat-item">
                        <span class="serial-label">Total Serial Count:</span>
                        <span id="totalSerialCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">Total Unique Serial Count:</span>
                        <span id="totalUniqueSerialCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">Total Credit Unique Serial Numbers:</span>
                        <span id="totalCreditUniqueSerial" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">Total Debit Unique Serial Numbers:</span>
                        <span id="totalDebitUniqueSerial" class="serial-value">0</span>
                    </div>
                </div>
            </div>

            <div class="serial-stats-section">
                <h4>HOC Analytics</h4>
                <div class="serial-stats-grid">
                    <div class="serial-stat-item">
                        <span class="serial-label">HOC Total Customer Count:</span>
                        <span id="hocTotalCustomerCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item clickable-stat-item" onclick="toggleHOCCurrencyTotals()">
                        <span class="serial-label">HOC Total Amount: <span id="hocCurrencyToggleIcon" class="toggle-icon-small">▼</span></span>
                        <span id="hocTotalAmount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">HOC Total Transaction Count:</span>
                        <span id="hocTotalTransactionCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">HOC Debit Customer Count:</span>
                        <span id="hocDebitCustomerCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">HOC Debit Transaction Count:</span>
                        <span id="hocDebitTransactionCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">HOC Debit Total Amount:</span>
                        <span id="hocDebitTotalAmount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">HOC Credit Customer Count:</span>
                        <span id="hocCreditCustomerCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">HOC Credit Transaction Count:</span>
                        <span id="hocCreditTransactionCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">HOC Credit Total Amount:</span>
                        <span id="hocCreditTotalAmount" class="serial-value">0</span>
                    </div>
                </div>
            </div>

            <!-- HOC Currency Breakdown -->
            <div id="hocCurrencyTotalsContent" class="currency-totals-content hidden">
                <div class="currency-totals-table-container">
                    <table class="currency-totals-table">
                        <thead>
                            <tr>
                                <th>Currency</th>
                                <th style="text-align: right;">HOC Total Amount</th>
                            </tr>
                        </thead>
                        <tbody id="hocCurrencyTotalsTableBody">
                            <!-- HOC Currency rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="serial-stats-section">
                <h4>IBD Analytics</h4>
                <div class="serial-stats-grid">
                    <div class="serial-stat-item">
                        <span class="serial-label">IBD Total Customer Count:</span>
                        <span id="ibdTotalCustomerCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item clickable-stat-item" onclick="toggleIBDCurrencyTotals()">
                        <span class="serial-label">IBD Total Amount: <span id="ibdCurrencyToggleIcon" class="toggle-icon-small">▼</span></span>
                        <span id="ibdTotalAmount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">IBD Total Transaction Count:</span>
                        <span id="ibdTotalTransactionCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">IBD Debit Customer Count:</span>
                        <span id="ibdDebitCustomerCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">IBD Debit Transaction Count:</span>
                        <span id="ibdDebitTransactionCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">IBD Debit Total Amount:</span>
                        <span id="ibdDebitTotalAmount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">IBD Credit Customer Count:</span>
                        <span id="ibdCreditCustomerCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">IBD Credit Transaction Count:</span>
                        <span id="ibdCreditTransactionCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">IBD Credit Total Amount:</span>
                        <span id="ibdCreditTotalAmount" class="serial-value">0</span>
                    </div>
                </div>
            </div>

            <!-- IBD Currency Breakdown -->
            <div id="ibdCurrencyTotalsContent" class="currency-totals-content hidden">
                <div class="currency-totals-table-container">
                    <table class="currency-totals-table">
                        <thead>
                            <tr>
                                <th>Currency</th>
                                <th style="text-align: right;">IBD Total Amount</th>
                            </tr>
                        </thead>
                        <tbody id="ibdCurrencyTotalsTableBody">
                            <!-- IBD Currency rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="serial-stats-section">
                <h4>WU Analytics</h4>
                <div class="serial-stats-grid">
                    <div class="serial-stat-item">
                        <span class="serial-label">WU Total Customer Count:</span>
                        <span id="wuTotalCustomerCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item clickable-stat-item" onclick="toggleWUCurrencyTotals()">
                        <span class="serial-label">WU Total Amount: <span id="wuCurrencyToggleIcon" class="toggle-icon-small">▼</span></span>
                        <span id="wuTotalAmount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">WU Total Transaction Count:</span>
                        <span id="wuTotalTransactionCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">WU Debit Customer Count:</span>
                        <span id="wuDebitCustomerCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">WU Debit Transaction Count:</span>
                        <span id="wuDebitTransactionCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">WU Debit Total Amount:</span>
                        <span id="wuDebitTotalAmount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">WU Credit Customer Count:</span>
                        <span id="wuCreditCustomerCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">WU Credit Transaction Count:</span>
                        <span id="wuCreditTransactionCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">WU Credit Total Amount:</span>
                        <span id="wuCreditTotalAmount" class="serial-value">0</span>
                    </div>
                </div>
            </div>

            <!-- WU Currency Breakdown -->
            <div id="wuCurrencyTotalsContent" class="currency-totals-content hidden">
                <div class="currency-totals-table-container">
                    <table class="currency-totals-table">
                        <thead>
                            <tr>
                                <th>Currency</th>
                                <th style="text-align: right;">WU Total Amount</th>
                            </tr>
                        </thead>
                        <tbody id="wuCurrencyTotalsTableBody">
                            <!-- WU Currency rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="serial-stats-section">
                <h4>High-Value Customer Analytics</h4>
                <div class="serial-stats-grid">
                    <div class="serial-stat-item">
                        <span class="serial-label">High-Value MMK Transactions:</span>
                        <span id="highValueMMKCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">High-Value MMK Total:</span>
                        <span id="highValueMMKTotal" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">High-Value USD Transactions:</span>
                        <span id="highValueUSDCount" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">High-Value USD Total:</span>
                        <span id="highValueUSDTotal" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">Unique High-Value MMK Customers:</span>
                        <span id="uniqueHighValueMMKCustomers" class="serial-value">0</span>
                    </div>
                    <div class="serial-stat-item">
                        <span class="serial-label">Unique High-Value USD Customers:</span>
                        <span id="uniqueHighValueUSDCustomers" class="serial-value">0</span>
                    </div>
                </div>
            </div>

            <div class="data-preview">
                <div style="display: flex; justify-content: center; margin: 40px 0;">
                    <button id="exportExcelBtn" style="background-color: #27ae60; padding: 10px 20px; font-size: 16px; border-radius: 5px; border: none; color: white; cursor: pointer;">Export to Excel</button>
                </div>
                <div class="table-container hidden">
                    <table>
                        <thead>
                            <tr id="headerRow"></tr>
                        </thead>
                        <tbody id="previewBody"></tbody>
                    </table>
                </div>
            </div>

            <!-- Transaction Summary section removed as requested -->

        </div>
    </div>

    <script>
        // Simplified debugger protection - prevent debugging interruptions
        (function() {
            // Basic debugger protection
            window.debugger = function() {};

            // Prevent memory crash popups
            const originalAlert = window.alert;
            const originalConfirm = window.confirm;

            window.alert = function(message) {
                if (typeof message === 'string' &&
                    (message.toLowerCase().includes('memory') ||
                     message.toLowerCase().includes('crash') ||
                     message.toLowerCase().includes('pause'))) {
                    console.log('Memory warning suppressed:', message);
                    return;
                }
                return originalAlert.call(this, message);
            };

            window.confirm = function(message) {
                if (typeof message === 'string' &&
                    (message.toLowerCase().includes('memory') ||
                     message.toLowerCase().includes('crash') ||
                     message.toLowerCase().includes('pause'))) {
                    console.log('Memory confirmation suppressed:', message);
                    return true; // Always continue
                }
                return originalConfirm.call(this, message);
            };

            // Prevent pausing on exceptions
            window.addEventListener('error', function(e) {
                console.log('Error caught:', e.message);
                e.preventDefault();
                return false;
            }, true);

            console.log('Basic protection enabled');
        })();

        // Keep page active to prevent Chrome optimizations
        setInterval(function() {
            try {
                // Simple activity simulation
                if (document.body) {
                    const dummy = document.createElement('span');
                    dummy.style.display = 'none';
                    document.body.appendChild(dummy);
                    document.body.removeChild(dummy);
                }
            } catch (e) {
                // Ignore errors
            }
        }, 5000);

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM Content Loaded - Initializing dashboard...');

            // DOM Elements
            const csvFileUpload = document.getElementById('csvFileUpload');
            const uploadBtn = document.getElementById('uploadBtn');
            const loadingIndicator = document.getElementById('loadingIndicator');
            const errorMessage = document.getElementById('errorMessage');
            const dashboardContainer = document.getElementById('dashboardContainer');
            
            // Add event listener to Export to Excel button
            const exportBtn = document.getElementById('exportExcelBtn');
            if (exportBtn) {
                exportBtn.addEventListener('click', exportToExcel);
            }

            console.log('Key elements found:', {
                csvFileUpload: !!csvFileUpload,
                uploadBtn: !!uploadBtn,
                loadingIndicator: !!loadingIndicator,
                errorMessage: !!errorMessage,
                dashboardContainer: !!dashboardContainer
            });
            const totalFilesEl = document.getElementById('totalFiles');
            const totalTransactionsEl = document.getElementById('totalTransactions');
            const totalAmountEl = document.getElementById('totalAmount');
            const avgTransactionEl = document.getElementById('avgTransaction');

            // Batch DOM update function for better performance
            function batchUpdateDashboard() {
                // Calculate values once
                const avgTransaction = globalStats.totalTransactions > 0 ? globalStats.totalAmount / globalStats.totalTransactions : 0;

                // Use document fragment for batch updates
                const updates = [
                    { el: totalFilesEl, value: globalStats.totalFiles.toLocaleString() },
                    { el: totalTransactionsEl, value: globalStats.totalTransactions.toLocaleString() },
                    { el: totalAmountEl, value: formatCurrency(globalStats.totalAmount) },
                    { el: avgTransactionEl, value: formatCurrency(avgTransaction) }
                ];

                // Batch update all elements
                updates.forEach(update => {
                    if (update.el) update.el.textContent = update.value;
                });
            }
            const headerRow = document.getElementById('headerRow');
            const previewBody = document.getElementById('previewBody');

            // Progress and memory elements
            const loadingText = document.getElementById('loadingText');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const memoryUsage = document.getElementById('memoryUsage');
            const filesProcessed = document.getElementById('filesProcessed');
            const currentBatch = document.getElementById('currentBatch');

            // Summary elements removed as requested

            // Serial statistics elements
            const totalSerialCountEl = document.getElementById('totalSerialCount');
            const totalUniqueSerialCountEl = document.getElementById('totalUniqueSerialCount');
            const totalCreditUniqueSerialEl = document.getElementById('totalCreditUniqueSerial');
            const totalDebitUniqueSerialEl = document.getElementById('totalDebitUniqueSerial');

            // Ultra-aggressive memory management for 80+ files
            const MEMORY_CONFIG = {
                MAX_MEMORY_MB: 1000, // 1GB limit (very conservative)
                BATCH_SIZE: 3, // Process only 3 files at a time
                GC_THRESHOLD: 600, // Trigger GC at 600MB (very early)
                CHUNK_SIZE: 100, // Process only 100 rows at a time
                EMERGENCY_THRESHOLD: 800, // Emergency cleanup at 800MB
                MAX_ROWS_IN_MEMORY: 50000, // Never keep more than 50k rows in memory
                ANALYSIS_CHUNK_SIZE: 10000 // Analyze data in 10k chunks
            };

            // Use streaming approach - don't store all data in memory
            let fileStats = [];
            let memoryMonitor = null;
            let globalStats = {
                totalFiles: 0,
                totalTransactions: 0,
                totalAmount: 0,
                allSerialNumbers: [],
                uniqueSerialNumbers: new Set(),
                creditSerialNumbers: new Set(),
                debitSerialNumbers: new Set(),
                sampleData: [] // Keep only first 10 rows for preview
            };

            // Enhanced Memory Guardian Functions
            function startMemoryMonitoring() {
                memoryMonitor = setInterval(() => {
                    try {
                        if (performance.memory) {
                            const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
                            updateMemoryDisplay(memoryMB);

                            // Emergency cleanup at higher threshold
                            if (memoryMB > MEMORY_CONFIG.EMERGENCY_THRESHOLD) {
                                console.log('Emergency memory cleanup triggered at', memoryMB, 'MB');
                                emergencyMemoryCleanup();
                                return true; // Continue but with emergency cleanup
                            }

                            // Regular cleanup
                            if (memoryMB > MEMORY_CONFIG.GC_THRESHOLD) {
                                console.log('Memory threshold reached, triggering cleanup...');
                                forceGarbageCollection();
                            }

                            // Never actually pause - just clean up more aggressively
                            if (memoryMB > MEMORY_CONFIG.MAX_MEMORY_MB) {
                                console.warn('Memory limit approached, aggressive cleanup...');
                                emergencyMemoryCleanup();
                                // Don't return false - keep processing but with cleanup
                            }
                        }
                        return true; // Always continue processing
                    } catch (e) {
                        // Ignore any errors in memory monitoring
                        console.log('Memory monitoring error ignored:', e.message);
                        return true;
                    }
                }, 500); // Check more frequently
            }

            function stopMemoryMonitoring() {
                if (memoryMonitor) {
                    clearInterval(memoryMonitor);
                    memoryMonitor = null;
                }
            }

            function forceGarbageCollection() {
                // Force garbage collection by creating and destroying large objects
                try {
                    // Multiple cleanup strategies
                    const temp1 = new Array(500000).fill(0);
                    temp1.length = 0;

                    const temp2 = new Array(500000).fill(null);
                    temp2.length = 0;

                    // Clear any temporary variables
                    if (window.gc) {
                        window.gc();
                    }

                    // Force browser to reclaim memory
                    if (window.CollectGarbage) {
                        window.CollectGarbage();
                    }
                } catch (e) {
                    // Ignore cleanup errors
                }
            }

            function emergencyMemoryCleanup() {
                try {
                    console.log('Emergency memory cleanup initiated...');

                    // Multiple aggressive cleanup attempts
                    for (let i = 0; i < 3; i++) {
                        forceGarbageCollection();
                    }

                    // Clear any large arrays or objects that might be hanging around
                    if (window.tempData) {
                        window.tempData = null;
                    }

                    // Force multiple GC cycles
                    setTimeout(() => forceGarbageCollection(), 100);
                    setTimeout(() => forceGarbageCollection(), 200);
                    setTimeout(() => forceGarbageCollection(), 300);

                    console.log('Emergency cleanup completed');
                } catch (e) {
                    // Ignore cleanup errors but continue
                    console.log('Emergency cleanup error ignored:', e.message);
                }
            }

            // Ultra-fast memory cleanup
            async function forceMemoryCleanup() {
                try {
                    // Quick cleanup
                    forceGarbageCollection();
                    await new Promise(resolve => setTimeout(resolve, 50));
                } catch (e) {
                    // Ignore errors
                }
            }

            // Major memory cleanup between batches
            async function majorMemoryCleanup() {
                try {
                    console.log('Major memory cleanup...');

                    // Clear any temporary variables
                    if (window.tempFileData) window.tempFileData = null;
                    if (window.tempRows) window.tempRows = null;

                    // Multiple GC cycles
                    for (let i = 0; i < 5; i++) {
                        forceGarbageCollection();
                        await new Promise(resolve => setTimeout(resolve, 100));
                    }

                    console.log('Major cleanup completed');
                } catch (e) {
                    console.log('Major cleanup error ignored:', e.message);
                }
            }

            // Stream process a single file without storing all data
            async function streamProcessFile(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();

                    reader.onload = async function(event) {
                        try {
                            const csvData = event.target.result;
                            const result = await analyzeCSVStreaming(csvData);
                            resolve(result);
                        } catch (error) {
                            console.warn(`Error processing ${file.name}:`, error.message);
                            // Return empty result instead of failing
                            resolve({
                                recordCount: 0,
                                totalAmount: 0,
                                totalMMK: 0,
                                totalUSD: 0,
                                highestAmount: 0,
                                lowestAmount: Infinity,
                                highValueMMKCount: 0,
                                highValueMMKTotal: 0,
                                highValueUSDCount: 0,
                                highValueUSDTotal: 0,
                                highValueMMKCustomers: new Set(),
                                highValueUSDCustomers: new Set(),
                                transactionTypes: {},
                                paymentMethods: {},
                                currencies: [],
                                dates: [],
                                serialNumbers: [],
                                creditSerials: [],
                                debitSerials: [],
                                // HOC analytics
                                hocTotalTransactionCount: 0,
                                hocTotalAmount: 0,
                                hocCustomers: new Set(),
                                hocDebitTransactionCount: 0,
                                hocDebitTotalAmount: 0,
                                hocDebitCustomers: new Set(),
                                hocCreditTransactionCount: 0,
                                hocCreditTotalAmount: 0,
                                hocCreditCustomers: new Set(),
                                // IBD analytics
                                ibdTotalTransactionCount: 0,
                                ibdTotalAmount: 0,
                                ibdCustomers: new Set(),
                                ibdDebitTransactionCount: 0,
                                ibdDebitTotalAmount: 0,
                                ibdDebitCustomers: new Set(),
                                ibdCreditTransactionCount: 0,
                                ibdCreditTotalAmount: 0,
                                ibdCreditCustomers: new Set(),
                                // WU analytics
                                wuTotalTransactionCount: 0,
                                wuTotalAmount: 0,
                                wuCustomers: new Set(),
                                wuDebitTransactionCount: 0,
                                wuDebitTotalAmount: 0,
                                wuDebitCustomers: new Set(),
                                wuCreditTransactionCount: 0,
                                wuCreditTotalAmount: 0,
                                wuCreditCustomers: new Set(),
                                sampleRows: []
                            });
                        }
                    };

                    reader.onerror = function() {
                        console.warn(`Error reading file: ${file.name}`);
                        resolve({
                            recordCount: 0,
                            totalAmount: 0,
                            totalMMK: 0,
                            totalUSD: 0,
                            highestAmount: 0,
                            lowestAmount: Infinity,
                            highValueMMKCount: 0,
                            highValueMMKTotal: 0,
                            highValueUSDCount: 0,
                            highValueUSDTotal: 0,
                            highValueMMKCustomers: new Set(),
                            highValueUSDCustomers: new Set(),
                            transactionTypes: {},
                            paymentMethods: {},
                            currencies: [],
                            dates: [],
                            serialNumbers: [],
                            creditSerials: [],
                            debitSerials: [],
                            // HOC analytics
                            hocTotalTransactionCount: 0,
                            hocTotalAmount: 0,
                            hocCustomers: new Set(),
                            hocDebitTransactionCount: 0,
                            hocDebitTotalAmount: 0,
                            hocDebitCustomers: new Set(),
                            hocCreditTransactionCount: 0,
                            hocCreditTotalAmount: 0,
                            hocCreditCustomers: new Set(),
                            // IBD analytics
                            ibdTotalTransactionCount: 0,
                            ibdTotalAmount: 0,
                            ibdCustomers: new Set(),
                            ibdDebitTransactionCount: 0,
                            ibdDebitTotalAmount: 0,
                            ibdDebitCustomers: new Set(),
                            ibdCreditTransactionCount: 0,
                            ibdCreditTotalAmount: 0,
                            ibdCreditCustomers: new Set(),
                            // WU analytics
                            wuTotalTransactionCount: 0,
                            wuTotalAmount: 0,
                            wuCustomers: new Set(),
                            wuDebitTransactionCount: 0,
                            wuDebitTotalAmount: 0,
                            wuDebitCustomers: new Set(),
                            wuCreditTransactionCount: 0,
                            wuCreditTotalAmount: 0,
                            wuCreditCustomers: new Set(),
                            sampleRows: []
                        });
                    };

                    reader.readAsText(file);
                });
            }

            function updateMemoryDisplay(memoryMB) {
                if (memoryUsage) memoryUsage.textContent = `${memoryMB} MB`;

                // Color code memory usage
                if (memoryUsage) {
                    if (memoryMB > MEMORY_CONFIG.GC_THRESHOLD) {
                        memoryUsage.style.color = '#e74c3c';
                    } else if (memoryMB > MEMORY_CONFIG.MAX_MEMORY_MB * 0.7) {
                        memoryUsage.style.color = '#f39c12';
                    } else {
                        memoryUsage.style.color = '#27ae60';
                    }
                }
            }

            function updateProgress(current, total, text = '') {
                const percentage = Math.round((current / total) * 100);
                if (progressFill) progressFill.style.width = `${percentage}%`;
                if (progressText) progressText.textContent = `${percentage}%`;
                if (filesProcessed) filesProcessed.textContent = current;

                if (text && loadingText) loadingText.textContent = text;
            }

            // Process CSV files on button click
            uploadBtn.addEventListener('click', function() {
                console.log('Upload button clicked!');

                const files = csvFileUpload.files;
                console.log('Files selected:', files.length);

                if (files.length === 0) {
                    console.log('No files selected, showing error');
                    showError('Please select at least one CSV file to upload.');
                    return;
                }

                console.log('Starting file processing...');

                // Performance monitoring
                const startTime = performance.now();
                console.log('🚀 Performance: Starting optimized file processing...');

                resetDashboard();
                showLoading();
                startMemoryMonitoring();

                // Process all files with memory management
                processMultipleFilesWithMemoryManagement(files);
            });

            // Ultra-fast streaming file processor for 80+ files
            async function processMultipleFilesWithMemoryManagement(files) {
                // Reset global stats
                globalStats = {
                    totalFiles: 0,
                    totalTransactions: 0,
                    totalAmount: 0,
                    totalMMK: 0,
                    totalUSD: 0,
                    totalCNY: 0,
                    totalEUR: 0,
                    totalINR: 0,
                    totalJPY: 0,
                    totalSGD: 0,
                    totalTHB: 0,
                    currencyTotals: {
                        MMK: 0,
                        USD: 0,
                        CNY: 0,
                        EUR: 0,
                        INR: 0,
                        JPY: 0,
                        SGD: 0,
                        THB: 0
                    },
                    hocCurrencyTotals: {
                        MMK: 0,
                        USD: 0,
                        CNY: 0,
                        EUR: 0,
                        INR: 0,
                        JPY: 0,
                        SGD: 0,
                        THB: 0
                    },
                    ibdCurrencyTotals: {
                        MMK: 0,
                        USD: 0,
                        CNY: 0,
                        EUR: 0,
                        INR: 0,
                        JPY: 0,
                        SGD: 0,
                        THB: 0
                    },
                    wuCurrencyTotals: {
                        MMK: 0,
                        USD: 0,
                        CNY: 0,
                        EUR: 0,
                        INR: 0,
                        JPY: 0,
                        SGD: 0,
                        THB: 0
                    },
                    highestAmount: 0,
                    lowestAmount: Infinity,
                    highValueMMKCount: 0,
                    highValueMMKTotal: 0,
                    highValueUSDCount: 0,
                    highValueUSDTotal: 0,
                    highValueMMKCustomers: new Set(),
                    highValueUSDCustomers: new Set(),
                    transactionTypes: {},
                    paymentMethods: {},
                    currencies: new Set(),
                    dates: [],
                    allSerialNumbers: [],
                    uniqueSerialNumbers: new Set(),
                    creditSerialNumbers: new Set(),
                    debitSerialNumbers: new Set(),
                    // HOC analytics
                    hocTotalTransactionCount: 0,
                    hocTotalAmount: 0,
                    hocCustomers: new Set(),
                    hocDebitTransactionCount: 0,
                    hocDebitTotalAmount: 0,
                    hocDebitCustomers: new Set(),
                    hocCreditTransactionCount: 0,
                    hocCreditTotalAmount: 0,
                    hocCreditCustomers: new Set(),
                    // IBD analytics
                    ibdTotalTransactionCount: 0,
                    ibdTotalAmount: 0,
                    ibdCustomers: new Set(),
                    ibdDebitTransactionCount: 0,
                    ibdDebitTotalAmount: 0,
                    ibdDebitCustomers: new Set(),
                    ibdCreditTransactionCount: 0,
                    ibdCreditTotalAmount: 0,
                    ibdCreditCustomers: new Set(),
                    // WU analytics
                    wuTotalTransactionCount: 0,
                    wuTotalAmount: 0,
                    wuCustomers: new Set(),
                    wuDebitTransactionCount: 0,
                    wuDebitTotalAmount: 0,
                    wuDebitCustomers: new Set(),
                    wuCreditTransactionCount: 0,
                    wuCreditTotalAmount: 0,
                    wuCreditCustomers: new Set(),
                    sampleData: []
                };
                fileStats = [];

                try {
                    const totalFiles = files.length;
                    let processedFiles = 0;
                    let currentBatchNum = 1;

                    console.log(`Starting ultra-fast processing of ${totalFiles} files...`);

                    // Process files in very small batches
                    for (let batchStart = 0; batchStart < totalFiles; batchStart += MEMORY_CONFIG.BATCH_SIZE) {
                        const batchEnd = Math.min(batchStart + MEMORY_CONFIG.BATCH_SIZE, totalFiles);
                        const batch = Array.from(files).slice(batchStart, batchEnd);

                        if (currentBatch) currentBatch.textContent = currentBatchNum;
                        updateProgress(processedFiles, totalFiles, `Batch ${currentBatchNum}/${Math.ceil(totalFiles/MEMORY_CONFIG.BATCH_SIZE)}`);

                        // Process current batch with parallel streaming analysis
                        const validFiles = batch.filter(file =>
                            file.type === 'text/csv' || file.name.endsWith('.csv')
                        );

                        if (validFiles.length === 0) {
                            processedFiles += batch.length;
                            continue;
                        }

                        // Process valid files in parallel
                        const filePromises = validFiles.map(async (file, index) => {
                            const fileIndex = batchStart + batch.indexOf(file);
                            updateProgress(fileIndex, totalFiles, `${file.name} (${Math.round(file.size/1024)}KB)`);
                            return await streamProcessFile(file);
                        });

                        const fileResults = await Promise.all(filePromises);

                        // Process results sequentially to avoid race conditions
                        for (const fileResult of fileResults) {

                            if (fileResult.recordCount > 0) {
                                // Update global stats incrementally
                                console.log('File result stats:', {
                                    recordCount: fileResult.recordCount,
                                    hocTotalTransactionCount: fileResult.hocTotalTransactionCount,
                                    hocCustomers: fileResult.hocCustomers.size,
                                    hocDebitTransactionCount: fileResult.hocDebitTransactionCount,
                                    hocCreditTransactionCount: fileResult.hocCreditTransactionCount
                                });
                                globalStats.totalFiles++;
                                globalStats.totalTransactions += fileResult.recordCount;
                                globalStats.totalAmount += fileResult.totalAmount;
                                globalStats.totalMMK += fileResult.totalMMK;
                                globalStats.totalUSD += fileResult.totalUSD;
                                globalStats.totalCNY += fileResult.totalCNY;
                                globalStats.totalEUR += fileResult.totalEUR;
                                globalStats.totalINR += fileResult.totalINR;
                                globalStats.totalJPY += fileResult.totalJPY;
                                globalStats.totalSGD += fileResult.totalSGD;
                                globalStats.totalTHB += fileResult.totalTHB;

                                // Merge currency totals
                                Object.keys(fileResult.currencyTotals).forEach(currency => {
                                    globalStats.currencyTotals[currency] += fileResult.currencyTotals[currency];
                                });

                                // Merge report-type-specific currency totals
                                Object.keys(fileResult.hocCurrencyTotals).forEach(currency => {
                                    globalStats.hocCurrencyTotals[currency] += fileResult.hocCurrencyTotals[currency];
                                });
                                Object.keys(fileResult.ibdCurrencyTotals).forEach(currency => {
                                    globalStats.ibdCurrencyTotals[currency] += fileResult.ibdCurrencyTotals[currency];
                                });
                                Object.keys(fileResult.wuCurrencyTotals).forEach(currency => {
                                    globalStats.wuCurrencyTotals[currency] += fileResult.wuCurrencyTotals[currency];
                                });
                                
                                // Update HOC analytics
                                globalStats.hocTotalTransactionCount += fileResult.hocTotalTransactionCount;
                                globalStats.hocTotalAmount += fileResult.hocTotalAmount;
                                fileResult.hocCustomers.forEach(customer => globalStats.hocCustomers.add(customer));
                                globalStats.hocDebitTransactionCount += fileResult.hocDebitTransactionCount;
                                globalStats.hocDebitTotalAmount += fileResult.hocDebitTotalAmount;
                                fileResult.hocDebitCustomers.forEach(customer => globalStats.hocDebitCustomers.add(customer));
                                globalStats.hocCreditTransactionCount += fileResult.hocCreditTransactionCount;
                                globalStats.hocCreditTotalAmount += fileResult.hocCreditTotalAmount;
                                fileResult.hocCreditCustomers.forEach(customer => globalStats.hocCreditCustomers.add(customer));
                                
                                // Update IBD analytics
                                globalStats.ibdTotalTransactionCount += fileResult.ibdTotalTransactionCount;
                                globalStats.ibdTotalAmount += fileResult.ibdTotalAmount;
                                fileResult.ibdCustomers.forEach(customer => globalStats.ibdCustomers.add(customer));
                                globalStats.ibdDebitTransactionCount += fileResult.ibdDebitTransactionCount;
                                globalStats.ibdDebitTotalAmount += fileResult.ibdDebitTotalAmount;
                                fileResult.ibdDebitCustomers.forEach(customer => globalStats.ibdDebitCustomers.add(customer));
                                globalStats.ibdCreditTransactionCount += fileResult.ibdCreditTransactionCount;
                                globalStats.ibdCreditTotalAmount += fileResult.ibdCreditTotalAmount;
                                fileResult.ibdCreditCustomers.forEach(customer => globalStats.ibdCreditCustomers.add(customer));
                                
                                // Update WU analytics
                                globalStats.wuTotalTransactionCount += fileResult.wuTotalTransactionCount;
                                globalStats.wuTotalAmount += fileResult.wuTotalAmount;
                                fileResult.wuCustomers.forEach(customer => globalStats.wuCustomers.add(customer));
                                globalStats.wuDebitTransactionCount += fileResult.wuDebitTransactionCount;
                                globalStats.wuDebitTotalAmount += fileResult.wuDebitTotalAmount;
                                fileResult.wuDebitCustomers.forEach(customer => globalStats.wuDebitCustomers.add(customer));
                                globalStats.wuCreditTransactionCount += fileResult.wuCreditTransactionCount;
                                globalStats.wuCreditTotalAmount += fileResult.wuCreditTotalAmount;
                                fileResult.wuCreditCustomers.forEach(customer => globalStats.wuCreditCustomers.add(customer));
                                
                                // Update high-value customer analytics metrics
                                globalStats.highValueMMKCount += fileResult.highValueMMKCount;
                                globalStats.highValueMMKTotal += fileResult.highValueMMKTotal;
                                globalStats.highValueUSDCount += fileResult.highValueUSDCount;
                                globalStats.highValueUSDTotal += fileResult.highValueUSDTotal;

                                // Merge unique high-value customer sets
                                if (fileResult.highValueMMKCustomers) {
                                    for (const customer of fileResult.highValueMMKCustomers) {
                                        globalStats.highValueMMKCustomers.add(customer);
                                    }
                                }
                                if (fileResult.highValueUSDCustomers) {
                                    for (const customer of fileResult.highValueUSDCustomers) {
                                        globalStats.highValueUSDCustomers.add(customer);
                                    }
                                }
                                
                                // Merge unique high-value customers sets
                                fileResult.highValueMMKCustomers.forEach(customer => {
                                    globalStats.highValueMMKCustomers.add(customer);
                                });
                                fileResult.highValueUSDCustomers.forEach(customer => {
                                    globalStats.highValueUSDCustomers.add(customer);
                                });

                                if (fileResult.highestAmount > globalStats.highestAmount) {
                                    globalStats.highestAmount = fileResult.highestAmount;
                                }
                                if (fileResult.lowestAmount < globalStats.lowestAmount && fileResult.lowestAmount > 0) {
                                    globalStats.lowestAmount = fileResult.lowestAmount;
                                }

                                // Merge stats
                                Object.keys(fileResult.transactionTypes).forEach(type => {
                                    globalStats.transactionTypes[type] = (globalStats.transactionTypes[type] || 0) + fileResult.transactionTypes[type];
                                });

                                Object.keys(fileResult.paymentMethods).forEach(method => {
                                    globalStats.paymentMethods[method] = (globalStats.paymentMethods[method] || 0) + fileResult.paymentMethods[method];
                                });

                                fileResult.currencies.forEach(currency => globalStats.currencies.add(currency));
                                globalStats.dates.push(...fileResult.dates);

                                // Serial number stats
                                fileResult.serialNumbers.forEach(serial => {
                                    globalStats.allSerialNumbers.push(serial);
                                    globalStats.uniqueSerialNumbers.add(serial);
                                });
                                fileResult.creditSerials.forEach(serial => globalStats.creditSerialNumbers.add(serial));
                                fileResult.debitSerials.forEach(serial => globalStats.debitSerialNumbers.add(serial));

                                // Keep only first few rows for preview
                                if (globalStats.sampleData.length < 10) {
                                    globalStats.sampleData.push(...fileResult.sampleRows.slice(0, 10 - globalStats.sampleData.length));
                                }

                                fileStats.push({
                                    name: validFiles[fileResults.indexOf(fileResult)].name,
                                    recordCount: fileResult.recordCount,
                                    totalAmount: fileResult.totalAmount
                                });
                            }
                        }

                        processedFiles += validFiles.length;

                        // Aggressive memory cleanup after each batch
                        if (processedFiles % 5 === 0) { // Every 5 files
                            updateProgress(processedFiles, totalFiles, 'Memory cleanup...');
                            await forceMemoryCleanup();
                        }

                        // Major cleanup between batches
                        updateProgress(processedFiles, totalFiles, 'Batch cleanup...');
                        await majorMemoryCleanup();

                        currentBatchNum++;
                    }

                    if (globalStats.totalTransactions === 0) {
                        showError('No transaction data found in the uploaded files.');
                        return;
                    }

                    updateProgress(totalFiles, totalFiles, 'Finalizing results...');
                    await new Promise(resolve => setTimeout(resolve, 100));

                    displayStreamingResults();

                    // Performance monitoring completion
                    if (typeof startTime !== 'undefined') {
                        const endTime = performance.now();
                        const processingTime = (endTime - startTime) / 1000;
                        const recordsPerSecond = Math.round(globalStats.totalTransactions / processingTime);
                        console.log(`🎯 Performance Results:`);
                        console.log(`   ⏱️  Total time: ${processingTime.toFixed(2)} seconds`);
                        console.log(`   📊 Records processed: ${globalStats.totalTransactions.toLocaleString()}`);
                        console.log(`   🚀 Processing speed: ${recordsPerSecond.toLocaleString()} records/second`);
                        console.log(`   📁 Files processed: ${globalStats.totalFiles}`);
                    }

                    stopMemoryMonitoring();
                    hideLoading();
                    dashboardContainer.classList.remove('hidden');

                } catch (error) {
                    // Never let errors completely stop the application
                    console.log('Processing error caught:', error.message);

                    try {
                        // Attempt to continue with whatever data we have
                        if (globalStats.totalTransactions > 0) {
                            updateProgress(totalFiles, totalFiles, 'Completing with available data...');
                            displayStreamingResults(); // Use displayStreamingResults instead of analyzeCombinedData
                            stopMemoryMonitoring();
                            hideLoading();
                            dashboardContainer.classList.remove('hidden');

                            // Show a warning but don't stop
                            const warningDiv = document.createElement('div');
                            warningDiv.style.cssText = 'background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; margin: 10px 0; border-radius: 4px; color: #856404;';
                            warningDiv.textContent = `Warning: Some files may not have been fully processed due to: ${error.message}`;
                            dashboardContainer.insertBefore(warningDiv, dashboardContainer.firstChild);
                        } else {
                            // Only show error if we have no data at all
                            stopMemoryMonitoring();
                            hideLoading();
                            showError('Error processing files: ' + error.message);
                        }
                    } catch (recoveryError) {
                        // Last resort - just show the original error
                        console.log('Recovery failed:', recoveryError.message);
                        stopMemoryMonitoring();
                        hideLoading();
                        showError('Error processing files: ' + error.message);
                    }
                }
            }

            // Function to process a single CSV file with chunking
            function processSingleFileWithChunking(file) {
                return new Promise((resolve, reject) => {
                    const reader = new FileReader();

                    reader.onload = async function(event) {
                        try {
                            const csvData = event.target.result;
                            const parsedData = await parseCSVWithChunking(csvData);
                            resolve(parsedData);
                        } catch (error) {
                            reject(error);
                        }
                    };

                    reader.onerror = function() {
                        reject(new Error(`Error reading file: ${file.name}`));
                    };

                    reader.readAsText(file);
                });
            }

            // Legacy function for backward compatibility
            function processSingleFile(file) {
                return processSingleFileWithChunking(file);
            }

            // Function to parse CSV data with chunking for memory management
            async function parseCSVWithChunking(csvText) {
                console.log('CSV Text length:', csvText.length);

                // Split lines and handle different line endings
                const lines = csvText.split(/\r?\n/);
                console.log('Total lines:', lines.length);

                const result = [];

                if (lines.length < 2) {
                    throw new Error('CSV file must contain at least a header row and one data row.');
                }

                // Extract header (first line) - handle both comma and tab separators
                let separator = ',';
                if (lines[0].includes('\t')) {
                    separator = '\t';
                }

                const headers = lines[0].split(separator).map(header => header.trim().replace(/^"|"$/g, ''));
                console.log('Headers found:', headers.length);

                if (headers.length === 0) {
                    throw new Error('No headers found in CSV file.');
                }

                // Process data rows in chunks
                let validRows = 0;
                const totalDataLines = lines.length - 1;

                for (let chunkStart = 1; chunkStart < lines.length; chunkStart += MEMORY_CONFIG.CHUNK_SIZE) {
                    const chunkEnd = Math.min(chunkStart + MEMORY_CONFIG.CHUNK_SIZE, lines.length);
                    const chunk = [];

                    for (let i = chunkStart; i < chunkEnd; i++) {
                        const line = lines[i].trim();
                        if (line === '') continue;

                        // Simple split approach first, then handle complex cases if needed
                        let row;
                        if (line.includes('"')) {
                            // Handle quoted values with commas inside using a more robust parser
                            row = parseCSVLine(line, separator);
                        } else {
                            // Simple split for non-quoted values
                            row = line.split(separator).map(cell => cell.trim());
                        }

                        if (row.length === 0) continue;

                        // Create object from headers and row values
                        const rowObject = {};
                        for (let j = 0; j < headers.length; j++) {
                            const value = j < row.length ? row[j].replace(/^"|"$/g, '').trim() : '';
                            rowObject[headers[j]] = value;
                        }

                        chunk.push(rowObject);
                        validRows++;
                    }

                    // Add chunk to result
                    result.push(...chunk);

                    // Allow UI updates and memory cleanup between chunks
                    if (chunkEnd < lines.length) {
                        await new Promise(resolve => setTimeout(resolve, 10));
                    }
                }

                console.log('Valid rows parsed:', validRows);
                return result;
            }

            // Legacy function for backward compatibility
            function parseCSV(csvText) {
                return parseCSVWithChunking(csvText);
            }

            // Pre-compiled regex patterns for performance
            const LINE_SPLIT_REGEX = /\r?\n/;
            const QUOTE_CLEANUP_REGEX = /^"|"$/g;
            const EMPTY_LINE_REGEX = /^\s*$/;

            // Ultra-fast streaming CSV analyzer - analyzes without storing all data
            async function analyzeCSVStreaming(csvText) {
                const lines = csvText.split(LINE_SPLIT_REGEX);

                if (lines.length < 2) {
                    throw new Error('CSV file must contain at least a header row and one data row.');
                }

                // Extract header with optimized separator detection
                const firstLine = lines[0];
                const separator = firstLine.includes('\t') ? '\t' : ',';

                // Optimized header extraction
                const headers = firstLine.split(separator);
                const headerCount = headers.length;

                // Pre-process headers for faster access and create index map
                const headerIndexMap = {};
                for (let i = 0; i < headerCount; i++) {
                    headers[i] = headers[i].trim().replace(QUOTE_CLEANUP_REGEX, '');
                    headerIndexMap[headers[i]] = i;
                }

                if (headers.length === 0) {
                    throw new Error('No headers found in CSV file.');
                }

                // Pre-calculate critical column indices for faster access
                const amountIndex = headerIndexMap['TRANSACTION_AMOUNT'] || -1;
                const currencyIndex = headerIndexMap['TRANSACTION_CURRENCY'] || -1;
                const accountIdIndex = headerIndexMap['ACCOUNT_HOLDER_ID_NUMBER'] || -1;
                const accountRoleIndex = headerIndexMap['ACCOUNT_HOLDER_ACCOUNT_ROLE'] || -1;
                const reportTypeIndex = headerIndexMap['REPORTTYPE'] || -1;
                const serialNumberIndex = headerIndexMap['SERIAL_NUMBER'] || -1;

                // Initialize result object
                const result = {
                    recordCount: 0,
                    totalAmount: 0,
                    totalMMK: 0,
                    totalUSD: 0,
                    totalCNY: 0,
                    totalEUR: 0,
                    totalINR: 0,
                    totalJPY: 0,
                    totalSGD: 0,
                    totalTHB: 0,
                    currencyTotals: {
                        MMK: 0,
                        USD: 0,
                        CNY: 0,
                        EUR: 0,
                        INR: 0,
                        JPY: 0,
                        SGD: 0,
                        THB: 0
                    },
                    hocCurrencyTotals: {
                        MMK: 0,
                        USD: 0,
                        CNY: 0,
                        EUR: 0,
                        INR: 0,
                        JPY: 0,
                        SGD: 0,
                        THB: 0
                    },
                    ibdCurrencyTotals: {
                        MMK: 0,
                        USD: 0,
                        CNY: 0,
                        EUR: 0,
                        INR: 0,
                        JPY: 0,
                        SGD: 0,
                        THB: 0
                    },
                    wuCurrencyTotals: {
                        MMK: 0,
                        USD: 0,
                        CNY: 0,
                        EUR: 0,
                        INR: 0,
                        JPY: 0,
                        SGD: 0,
                        THB: 0
                    },
                    highestAmount: 0,
                    lowestAmount: Infinity,
                    highValueMMKCount: 0,
                    highValueMMKTotal: 0,
                    highValueUSDCount: 0,
                    highValueUSDTotal: 0,
                    highValueMMKCustomers: new Set(),
                    highValueUSDCustomers: new Set(),
                    transactionTypes: {},
                    paymentMethods: {},
                    currencies: [],
                    dates: [],
                    serialNumbers: [],
                    creditSerials: [],
                    debitSerials: [],
                    // HOC analytics
                    hocTotalTransactionCount: 0,
                    hocTotalAmount: 0,
                    hocCustomers: new Set(),
                    hocDebitTransactionCount: 0,
                    hocDebitTotalAmount: 0,
                    hocDebitCustomers: new Set(),
                    hocCreditTransactionCount: 0,
                    hocCreditTotalAmount: 0,
                    hocCreditCustomers: new Set(),
                    // IBD analytics
                    ibdTotalTransactionCount: 0,
                    ibdTotalAmount: 0,
                    ibdCustomers: new Set(),
                    ibdDebitTransactionCount: 0,
                    ibdDebitTotalAmount: 0,
                    ibdDebitCustomers: new Set(),
                    ibdCreditTransactionCount: 0,
                    ibdCreditTotalAmount: 0,
                    ibdCreditCustomers: new Set(),
                    // WU analytics
                    wuTotalTransactionCount: 0,
                    wuTotalAmount: 0,
                    wuCustomers: new Set(),
                    wuDebitTransactionCount: 0,
                    wuDebitTotalAmount: 0,
                    wuDebitCustomers: new Set(),
                    wuCreditTransactionCount: 0,
                    wuCreditTotalAmount: 0,
                    wuCreditCustomers: new Set(),
                    sampleRows: []
                };

                const currencySet = new Set();
                let sampleCount = 0;
                const linesLength = lines.length;

                // Pre-allocate row object for reuse
                const rowObject = {};

                // Process data rows with progressive loading for better UI responsiveness
                return new Promise((resolve) => {
                    let i = 1;
                    const BATCH_SIZE = 1000; // Process 1000 rows at a time

                    function processBatch() {
                        const batchEnd = Math.min(i + BATCH_SIZE, linesLength);

                        for (; i < batchEnd; i++) {
                    const line = lines[i];

                    // Fast empty line check
                    if (!line || EMPTY_LINE_REGEX.test(line)) continue;

                    let row;
                    if (line.indexOf('"') !== -1) {
                        row = parseCSVLine(line, separator);
                    } else {
                        row = line.split(separator);
                    }

                    if (row.length === 0) continue;

                    // Reuse row object for better performance
                    for (let k = 0; k < headerCount; k++) {
                        if (k < row.length) {
                            const cell = row[k];
                            rowObject[headers[k]] = cell.charAt(0) === '"' && cell.charAt(cell.length - 1) === '"'
                                ? cell.slice(1, -1).trim()
                                : cell.trim();
                        } else {
                            rowObject[headers[k]] = '';
                        }
                    }

                        // Analyze this row immediately (streaming analysis)
                        result.recordCount++;

                        // Use array indexing for faster access to critical fields
                        const amount = amountIndex >= 0 ? (parseFloat(row[amountIndex]) || 0) : 0;
                        result.totalAmount += amount;

                        // Track currency-specific totals using array indexing
                        const currency = currencyIndex >= 0 ? row[currencyIndex] : '';
                        const accountHolderId = accountIdIndex >= 0 ? row[accountIdIndex] : '';
                        const reportType = reportTypeIndex >= 0 ? row[reportTypeIndex] : '';
                        const accountRole = accountRoleIndex >= 0 ? row[accountRoleIndex] : '';

                        // Row data processing (debug removed for performance)
                        
                        // Track currency totals
                        if (currency && result.currencyTotals.hasOwnProperty(currency)) {
                            result.currencyTotals[currency] += amount;
                        }

                        if (currency === 'MMK') {
                            result.totalMMK += amount;

                            // Track high-value MMK transactions (≥1B MMK)
                            if (amount >= **********) {
                                result.highValueMMKCount++;
                                result.highValueMMKTotal += amount;

                                // Track unique high-value MMK customers
                                if (accountHolderId) {
                                    result.highValueMMKCustomers.add(accountHolderId);
                                }
                            }
                        } else if (currency === 'USD') {
                            result.totalUSD += amount;

                            // Track high-value USD transactions (≥10K USD)
                            if (amount >= 10000) {
                                result.highValueUSDCount++;
                                result.highValueUSDTotal += amount;

                                // Track unique high-value USD customers
                                if (accountHolderId) {
                                    result.highValueUSDCustomers.add(accountHolderId);
                                }
                            }
                        } else if (currency === 'CNY') {
                            result.totalCNY += amount;
                        } else if (currency === 'EUR') {
                            result.totalEUR += amount;
                        } else if (currency === 'INR') {
                            result.totalINR += amount;
                        } else if (currency === 'JPY') {
                            result.totalJPY += amount;
                        } else if (currency === 'SGD') {
                            result.totalSGD += amount;
                        } else if (currency === 'THB') {
                            result.totalTHB += amount;
                        }

                        if (amount > result.highestAmount) result.highestAmount = amount;
                        if (amount < result.lowestAmount && amount > 0) result.lowestAmount = amount;

                        // Count transaction types
                        const type = rowObject.TYPE_OF_BANK_TRANSACTION || 'Unknown';
                        result.transactionTypes[type] = (result.transactionTypes[type] || 0) + 1;

                        // Count payment methods
                        const method = rowObject.PAYMENT_METHOD || 'Unknown';
                        result.paymentMethods[method] = (result.paymentMethods[method] || 0) + 1;

                        // Collect currencies
                        if (currency) currencySet.add(currency);

                        // Collect dates
                        const date = rowObject.TRANSACTION_DATE;
                        if (date) result.dates.push(date);

                        // Serial number analysis using array indexing
                        const serialNo = serialNumberIndex >= 0 ? row[serialNumberIndex] : (rowObject.SERIAL_NO || '');

                        if (serialNo) {
                            result.serialNumbers.push(serialNo);

                            if (accountRole === 'C') {
                                result.creditSerials.push(serialNo);
                            } else if (accountRole === 'D') {
                                result.debitSerials.push(serialNo);
                            }
                        }
                        
                        // Report type analysis using array indexing for performance
                        if (reportType === 'HOC') {
                            result.hocTotalTransactionCount++;
                            result.hocTotalAmount += amount;
                            if (accountHolderId) result.hocCustomers.add(accountHolderId);

                            // Track HOC currency totals
                            if (currency && result.hocCurrencyTotals.hasOwnProperty(currency)) {
                                result.hocCurrencyTotals[currency] += amount;
                            }

                            if (accountRole === 'D') {
                                result.hocDebitTransactionCount++;
                                result.hocDebitTotalAmount += amount;
                                if (accountHolderId) result.hocDebitCustomers.add(accountHolderId);
                            } else if (accountRole === 'C') {
                                result.hocCreditTransactionCount++;
                                result.hocCreditTotalAmount += amount;
                                if (accountHolderId) result.hocCreditCustomers.add(accountHolderId);
                            }
                        } else if (reportType === 'IBD') {
                            result.ibdTotalTransactionCount++;
                            result.ibdTotalAmount += amount;
                            if (accountHolderId) result.ibdCustomers.add(accountHolderId);

                            // Track IBD currency totals
                            if (currency && result.ibdCurrencyTotals.hasOwnProperty(currency)) {
                                result.ibdCurrencyTotals[currency] += amount;
                            }

                            if (accountRole === 'D') {
                                result.ibdDebitTransactionCount++;
                                result.ibdDebitTotalAmount += amount;
                                if (accountHolderId) result.ibdDebitCustomers.add(accountHolderId);
                            } else if (accountRole === 'C') {
                                result.ibdCreditTransactionCount++;
                                result.ibdCreditTotalAmount += amount;
                                if (accountHolderId) result.ibdCreditCustomers.add(accountHolderId);
                            }
                        } else if (reportType === 'WU') {
                            result.wuTotalTransactionCount++;
                            result.wuTotalAmount += amount;
                            if (accountHolderId) result.wuCustomers.add(accountHolderId);

                            // Track WU currency totals
                            if (currency && result.wuCurrencyTotals.hasOwnProperty(currency)) {
                                result.wuCurrencyTotals[currency] += amount;
                            }

                            if (accountRole === 'D') {
                                result.wuDebitTransactionCount++;
                                result.wuDebitTotalAmount += amount;
                                if (accountHolderId) result.wuDebitCustomers.add(accountHolderId);
                            } else if (accountRole === 'C') {
                                result.wuCreditTransactionCount++;
                                result.wuCreditTotalAmount += amount;
                                if (accountHolderId) result.wuCreditCustomers.add(accountHolderId);
                            }
                        }

                            // Keep only first few rows for preview
                            if (sampleCount < 10) {
                                result.sampleRows.push(rowObject);
                                sampleCount++;
                            }
                        }

                        // Check if we need to yield control back to browser
                        if (i < linesLength) {
                            requestAnimationFrame(processBatch);
                        } else {
                            // Processing complete
                            result.currencies = Array.from(currencySet);
                            resolve(result);
                        }
                    }

                    // Start processing
                    processBatch();
                });
            }

            // Optimized CSV line parser with better performance
            function parseCSVLine(line, separator = ',') {
                const result = [];
                let inQuotes = false;
                let currentValue = '';
                const lineLength = line.length;

                for (let i = 0; i < lineLength; i++) {
                    const char = line.charAt(i);

                    if (char === '"') {
                        if (inQuotes && i + 1 < lineLength && line.charAt(i + 1) === '"') {
                            // Handle escaped quotes
                            currentValue += '"';
                            i++; // Skip next quote
                        } else {
                            // Toggle quote state
                            inQuotes = !inQuotes;
                        }
                    } else if (char === separator && !inQuotes) {
                        // End of field
                        result.push(currentValue);
                        currentValue = '';
                    } else {
                        currentValue += char;
                    }
                }

                // Add the last value
                result.push(currentValue);

                return result;
            }

            // Display results from streaming analysis
            function displayStreamingResults() {
                console.log('Displaying streaming results...');

                // Calculate average transaction amount
                const avgTransaction = globalStats.totalTransactions > 0 ? globalStats.totalAmount / globalStats.totalTransactions : 0;

                // Find most common transaction type and payment method
                const mostCommonType = Object.keys(globalStats.transactionTypes).reduce((a, b) =>
                    globalStats.transactionTypes[a] > globalStats.transactionTypes[b] ? a : b, 'N/A');
                const mostCommonMethod = Object.keys(globalStats.paymentMethods).reduce((a, b) =>
                    globalStats.paymentMethods[a] > globalStats.paymentMethods[b] ? a : b, 'N/A');

                // Calculate date range
                let dateRange = 'N/A';
                if (globalStats.dates.length > 0) {
                    const sortedDates = globalStats.dates.filter(d => d).sort();
                    if (sortedDates.length > 0) {
                        const firstDate = sortedDates[0];
                        const lastDate = sortedDates[sortedDates.length - 1];
                        dateRange = firstDate === lastDate ? firstDate : `${firstDate} to ${lastDate}`;
                    }
                }

                // Update dashboard metrics with batch update
                batchUpdateDashboard();
                if (document.getElementById('totalMMK')) document.getElementById('totalMMK').textContent = formatCurrency(globalStats.totalMMK);
                if (document.getElementById('totalUSD')) document.getElementById('totalUSD').textContent = formatCurrency(globalStats.totalUSD);

                // Update currency totals display
                updateCurrencyTotalsDisplay();
                updateHOCCurrencyTotalsDisplay();
                updateIBDCurrencyTotalsDisplay();
                updateWUCurrencyTotalsDisplay();

                // Transaction Summary section removed as requested

                // Update serial statistics
                if (totalSerialCountEl) totalSerialCountEl.textContent = globalStats.allSerialNumbers.length.toLocaleString();
                if (totalUniqueSerialCountEl) totalUniqueSerialCountEl.textContent = globalStats.uniqueSerialNumbers.size.toLocaleString();
                if (totalCreditUniqueSerialEl) totalCreditUniqueSerialEl.textContent = globalStats.creditSerialNumbers.size.toLocaleString();
                if (totalDebitUniqueSerialEl) totalDebitUniqueSerialEl.textContent = globalStats.debitSerialNumbers.size.toLocaleString();
                
                // Update HOC analytics
                if (document.getElementById('hocTotalCustomerCount')) document.getElementById('hocTotalCustomerCount').textContent = globalStats.hocCustomers.size.toLocaleString();
                if (document.getElementById('hocTotalAmount')) document.getElementById('hocTotalAmount').textContent = formatCurrency(globalStats.hocTotalAmount);
                if (document.getElementById('hocTotalTransactionCount')) document.getElementById('hocTotalTransactionCount').textContent = globalStats.hocTotalTransactionCount.toLocaleString();
                if (document.getElementById('hocDebitCustomerCount')) document.getElementById('hocDebitCustomerCount').textContent = globalStats.hocDebitCustomers.size.toLocaleString();
                if (document.getElementById('hocDebitTransactionCount')) document.getElementById('hocDebitTransactionCount').textContent = globalStats.hocDebitTransactionCount.toLocaleString();
                if (document.getElementById('hocDebitTotalAmount')) document.getElementById('hocDebitTotalAmount').textContent = formatCurrency(globalStats.hocDebitTotalAmount);
                if (document.getElementById('hocCreditCustomerCount')) document.getElementById('hocCreditCustomerCount').textContent = globalStats.hocCreditCustomers.size.toLocaleString();
                if (document.getElementById('hocCreditTransactionCount')) document.getElementById('hocCreditTransactionCount').textContent = globalStats.hocCreditTransactionCount.toLocaleString();
                if (document.getElementById('hocCreditTotalAmount')) document.getElementById('hocCreditTotalAmount').textContent = formatCurrency(globalStats.hocCreditTotalAmount);
                
                // Debug HOC stats
                console.log('HOC Stats:', {
                    hocTotalCustomerCount: globalStats.hocCustomers.size,
                    hocTotalAmount: globalStats.hocTotalAmount,
                    hocTotalTransactionCount: globalStats.hocTotalTransactionCount,
                    hocDebitCustomerCount: globalStats.hocDebitCustomers.size,
                    hocDebitTransactionCount: globalStats.hocDebitTransactionCount,
                    hocDebitTotalAmount: globalStats.hocDebitTotalAmount,
                    hocCreditCustomerCount: globalStats.hocCreditCustomers.size,
                    hocCreditTransactionCount: globalStats.hocCreditTransactionCount,
                    hocCreditTotalAmount: globalStats.hocCreditTotalAmount
                });
                
                // Update IBD analytics
                if (document.getElementById('ibdTotalCustomerCount')) document.getElementById('ibdTotalCustomerCount').textContent = globalStats.ibdCustomers.size.toLocaleString();
                if (document.getElementById('ibdTotalAmount')) document.getElementById('ibdTotalAmount').textContent = formatCurrency(globalStats.ibdTotalAmount);
                if (document.getElementById('ibdTotalTransactionCount')) document.getElementById('ibdTotalTransactionCount').textContent = globalStats.ibdTotalTransactionCount.toLocaleString();
                if (document.getElementById('ibdDebitCustomerCount')) document.getElementById('ibdDebitCustomerCount').textContent = globalStats.ibdDebitCustomers.size.toLocaleString();
                if (document.getElementById('ibdDebitTransactionCount')) document.getElementById('ibdDebitTransactionCount').textContent = globalStats.ibdDebitTransactionCount.toLocaleString();
                if (document.getElementById('ibdDebitTotalAmount')) document.getElementById('ibdDebitTotalAmount').textContent = formatCurrency(globalStats.ibdDebitTotalAmount);
                if (document.getElementById('ibdCreditCustomerCount')) document.getElementById('ibdCreditCustomerCount').textContent = globalStats.ibdCreditCustomers.size.toLocaleString();
                if (document.getElementById('ibdCreditTransactionCount')) document.getElementById('ibdCreditTransactionCount').textContent = globalStats.ibdCreditTransactionCount.toLocaleString();
                if (document.getElementById('ibdCreditTotalAmount')) document.getElementById('ibdCreditTotalAmount').textContent = formatCurrency(globalStats.ibdCreditTotalAmount);
                
                // Update WU analytics
                if (document.getElementById('wuTotalCustomerCount')) document.getElementById('wuTotalCustomerCount').textContent = globalStats.wuCustomers.size.toLocaleString();
                if (document.getElementById('wuTotalAmount')) document.getElementById('wuTotalAmount').textContent = formatCurrency(globalStats.wuTotalAmount);
                if (document.getElementById('wuTotalTransactionCount')) document.getElementById('wuTotalTransactionCount').textContent = globalStats.wuTotalTransactionCount.toLocaleString();
                if (document.getElementById('wuDebitCustomerCount')) document.getElementById('wuDebitCustomerCount').textContent = globalStats.wuDebitCustomers.size.toLocaleString();
                if (document.getElementById('wuDebitTransactionCount')) document.getElementById('wuDebitTransactionCount').textContent = globalStats.wuDebitTransactionCount.toLocaleString();
                if (document.getElementById('wuDebitTotalAmount')) document.getElementById('wuDebitTotalAmount').textContent = formatCurrency(globalStats.wuDebitTotalAmount);
                if (document.getElementById('wuCreditCustomerCount')) document.getElementById('wuCreditCustomerCount').textContent = globalStats.wuCreditCustomers.size.toLocaleString();
                if (document.getElementById('wuCreditTransactionCount')) document.getElementById('wuCreditTransactionCount').textContent = globalStats.wuCreditTransactionCount.toLocaleString();
                if (document.getElementById('wuCreditTotalAmount')) document.getElementById('wuCreditTotalAmount').textContent = formatCurrency(globalStats.wuCreditTotalAmount);
                
                // Update high-value customer analytics
                if (document.getElementById('highValueMMKCount')) document.getElementById('highValueMMKCount').textContent = globalStats.highValueMMKCount.toLocaleString();
                if (document.getElementById('highValueMMKTotal')) document.getElementById('highValueMMKTotal').textContent = formatCurrency(globalStats.highValueMMKTotal);
                if (document.getElementById('highValueUSDCount')) document.getElementById('highValueUSDCount').textContent = globalStats.highValueUSDCount.toLocaleString();
                if (document.getElementById('highValueUSDTotal')) document.getElementById('highValueUSDTotal').textContent = formatCurrency(globalStats.highValueUSDTotal);
                if (document.getElementById('uniqueHighValueMMKCustomers')) document.getElementById('uniqueHighValueMMKCustomers').textContent = globalStats.highValueMMKCustomers.size.toLocaleString();
                if (document.getElementById('uniqueHighValueUSDCustomers')) document.getElementById('uniqueHighValueUSDCustomers').textContent = globalStats.highValueUSDCustomers.size.toLocaleString();

                // Display file statistics
                // displayFileStatistics(); // This function is removed

                // Create data preview table from sample data
                if (globalStats.sampleData.length > 0) {
                    const columns = Object.keys(globalStats.sampleData[0]);
                    createDataPreview(globalStats.sampleData, columns);
                }

                console.log('Results displayed successfully');
            }

            // Legacy function for backward compatibility
            function analyzeCombinedData() {
                displayStreamingResults();
            }

            // Function to create data preview table
            function createDataPreview(data, columns) {
                // Clear existing header and rows
                if (headerRow) headerRow.innerHTML = '';
                if (previewBody) previewBody.innerHTML = '';

                if (data.length === 0) return;

                // Add headers
                columns.forEach(column => {
                    const th = document.createElement('th');
                    th.textContent = column;
                    if (headerRow) headerRow.appendChild(th);
                });

                // Add data rows (first 10 only)
                const previewData = data.slice(0, 10);
                previewData.forEach(row => {
                    const tr = document.createElement('tr');
                    columns.forEach(column => {
                        const td = document.createElement('td');
                        td.textContent = row[column] || '';
                        if (tr) tr.appendChild(td);
                    });
                    if (previewBody) previewBody.appendChild(tr);
                });
                
                // Make sure the Export to Excel button has the click event listener
                const exportBtn = document.getElementById('exportExcelBtn');
                if (exportBtn) {
                    // Remove any existing event listeners
                    exportBtn.replaceWith(exportBtn.cloneNode(true));
                    // Add the click event listener
                    document.getElementById('exportExcelBtn').addEventListener('click', exportToExcel);
                }
            }
            
            // Function to format currency for export (without special symbols)
            function formatCurrencyForExport(amount, currencyCode) {
                const options = {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                };

                // Special formatting for certain currencies
                if (currencyCode === 'JPY') {
                    options.minimumFractionDigits = 0;
                    options.maximumFractionDigits = 0;
                }

                const formattedNumber = amount.toLocaleString('en-US', options);
                return `${currencyCode} ${formattedNumber}`;
            }

            // Function to export data to Excel
            function exportToExcel() {
                // Create a CSV string with dashboard statistics
                let csvContent = '';

                // Add header
                csvContent += 'Transaction Dashboard Statistics Export\n';
                csvContent += 'Generated on: ' + new Date().toLocaleString() + '\n\n';

                // Add dashboard metrics section
                csvContent += '=== GENERAL METRICS ===\n';
                csvContent += 'Metric,Value\n';
                csvContent += 'Total Files,' + (globalStats.totalFiles || 0).toLocaleString() + '\n';
                csvContent += 'Total Transactions,' + (globalStats.totalTransactions || 0).toLocaleString() + '\n';
                csvContent += 'Total Amount,' + formatCurrency(globalStats.totalAmount || 0) + '\n';

                // Calculate average transaction amount
                const avgTransaction = globalStats.totalTransactions > 0 ? globalStats.totalAmount / globalStats.totalTransactions : 0;
                csvContent += 'Average Transaction,' + formatCurrency(avgTransaction) + '\n';
                csvContent += 'Total MMK,' + formatCurrency(globalStats.totalMMK || 0) + '\n';
                csvContent += 'Total USD,' + formatCurrency(globalStats.totalUSD || 0) + '\n\n';
                
                // Add serial number statistics section
                csvContent += '=== SERIAL NUMBER STATISTICS ===\n';
                csvContent += 'Metric,Value\n';
                csvContent += 'Total Serial Count,' + (globalStats.allSerialNumbers ? globalStats.allSerialNumbers.length : 0).toLocaleString() + '\n';
                csvContent += 'Total Unique Serial Count,' + (globalStats.uniqueSerialNumbers ? globalStats.uniqueSerialNumbers.size : 0).toLocaleString() + '\n';
                csvContent += 'Total Credit Unique Serial Numbers,' + (globalStats.creditSerialNumbers ? globalStats.creditSerialNumbers.size : 0).toLocaleString() + '\n';
                csvContent += 'Total Debit Unique Serial Numbers,' + (globalStats.debitSerialNumbers ? globalStats.debitSerialNumbers.size : 0).toLocaleString() + '\n\n';
                
                // Add HOC analytics section
                csvContent += '=== HOC ANALYTICS ===\n';
                csvContent += 'Metric,Value\n';
                csvContent += 'HOC Total Customer Count,' + (globalStats.hocCustomers ? globalStats.hocCustomers.size : 0).toLocaleString() + '\n';
                csvContent += 'HOC Total Amount,' + formatCurrency(globalStats.hocTotalAmount || 0) + '\n';
                csvContent += 'HOC Total Transaction Count,' + (globalStats.hocTotalTransactionCount || 0).toLocaleString() + '\n';
                csvContent += 'HOC Debit Customer Count,' + (globalStats.hocDebitCustomers ? globalStats.hocDebitCustomers.size : 0).toLocaleString() + '\n';
                csvContent += 'HOC Debit Transaction Count,' + (globalStats.hocDebitTransactionCount || 0).toLocaleString() + '\n';
                csvContent += 'HOC Debit Total Amount,' + formatCurrency(globalStats.hocDebitTotalAmount || 0) + '\n';
                csvContent += 'HOC Credit Customer Count,' + (globalStats.hocCreditCustomers ? globalStats.hocCreditCustomers.size : 0).toLocaleString() + '\n';
                csvContent += 'HOC Credit Transaction Count,' + (globalStats.hocCreditTransactionCount || 0).toLocaleString() + '\n';
                csvContent += 'HOC Credit Total Amount,' + formatCurrency(globalStats.hocCreditTotalAmount || 0) + '\n\n';
                
                // Add IBD analytics section
                csvContent += '=== IBD ANALYTICS ===\n';
                csvContent += 'Metric,Value\n';
                csvContent += 'IBD Total Customer Count,' + (globalStats.ibdCustomers ? globalStats.ibdCustomers.size : 0).toLocaleString() + '\n';
                csvContent += 'IBD Total Amount,' + formatCurrency(globalStats.ibdTotalAmount || 0) + '\n';
                csvContent += 'IBD Total Transaction Count,' + (globalStats.ibdTotalTransactionCount || 0).toLocaleString() + '\n';
                csvContent += 'IBD Debit Customer Count,' + (globalStats.ibdDebitCustomers ? globalStats.ibdDebitCustomers.size : 0).toLocaleString() + '\n';
                csvContent += 'IBD Debit Transaction Count,' + (globalStats.ibdDebitTransactionCount || 0).toLocaleString() + '\n';
                csvContent += 'IBD Debit Total Amount,' + formatCurrency(globalStats.ibdDebitTotalAmount || 0) + '\n';
                csvContent += 'IBD Credit Customer Count,' + (globalStats.ibdCreditCustomers ? globalStats.ibdCreditCustomers.size : 0).toLocaleString() + '\n';
                csvContent += 'IBD Credit Transaction Count,' + (globalStats.ibdCreditTransactionCount || 0).toLocaleString() + '\n';
                csvContent += 'IBD Credit Total Amount,' + formatCurrency(globalStats.ibdCreditTotalAmount || 0) + '\n\n';
                
                // Add WU analytics section
                csvContent += '=== WU ANALYTICS ===\n';
                csvContent += 'Metric,Value\n';
                csvContent += 'WU Total Customer Count,' + (globalStats.wuCustomers ? globalStats.wuCustomers.size : 0).toLocaleString() + '\n';
                csvContent += 'WU Total Amount,' + formatCurrency(globalStats.wuTotalAmount || 0) + '\n';
                csvContent += 'WU Total Transaction Count,' + (globalStats.wuTotalTransactionCount || 0).toLocaleString() + '\n';
                csvContent += 'WU Debit Customer Count,' + (globalStats.wuDebitCustomers ? globalStats.wuDebitCustomers.size : 0).toLocaleString() + '\n';
                csvContent += 'WU Debit Transaction Count,' + (globalStats.wuDebitTransactionCount || 0).toLocaleString() + '\n';
                csvContent += 'WU Debit Total Amount,' + formatCurrency(globalStats.wuDebitTotalAmount || 0) + '\n';
                csvContent += 'WU Credit Customer Count,' + (globalStats.wuCreditCustomers ? globalStats.wuCreditCustomers.size : 0).toLocaleString() + '\n';
                csvContent += 'WU Credit Transaction Count,' + (globalStats.wuCreditTransactionCount || 0).toLocaleString() + '\n';
                csvContent += 'WU Credit Total Amount,' + formatCurrency(globalStats.wuCreditTotalAmount || 0) + '\n\n';
                
                // Add high-value customer analytics section
                csvContent += '=== HIGH-VALUE CUSTOMER ANALYTICS ===\n';
                csvContent += 'Metric,Value\n';
                csvContent += 'High-Value MMK Transactions,' + (globalStats.highValueMMKCount || 0).toLocaleString() + '\n';
                csvContent += 'High-Value MMK Total,' + formatCurrency(globalStats.highValueMMKTotal || 0) + '\n';
                csvContent += 'High-Value USD Transactions,' + (globalStats.highValueUSDCount || 0).toLocaleString() + '\n';
                csvContent += 'High-Value USD Total,' + formatCurrency(globalStats.highValueUSDTotal || 0) + '\n';
                csvContent += 'Unique High-Value MMK Customers,' + (globalStats.highValueMMKCustomers ? globalStats.highValueMMKCustomers.size : 0).toLocaleString() + '\n';
                csvContent += 'Unique High-Value USD Customers,' + (globalStats.highValueUSDCustomers ? globalStats.highValueUSDCustomers.size : 0).toLocaleString() + '\n\n';

                // Add overall currency breakdown section
                csvContent += '=== OVERALL CURRENCY BREAKDOWN ===\n';
                csvContent += 'Currency,Total Amount\n';
                const supportedCurrencies = ['MMK', 'USD', 'CNY', 'EUR', 'INR', 'JPY', 'SGD', 'THB'];
                supportedCurrencies.forEach(currency => {
                    const amount = globalStats.currencyTotals[currency] || 0;
                    csvContent += `${currency},"${formatCurrencyForExport(amount, currency)}"\n`;
                });
                csvContent += '\n';

                // Add HOC currency breakdown section
                csvContent += '=== HOC CURRENCY BREAKDOWN ===\n';
                csvContent += 'Currency,HOC Total Amount\n';
                supportedCurrencies.forEach(currency => {
                    const amount = globalStats.hocCurrencyTotals[currency] || 0;
                    csvContent += `${currency},"${formatCurrencyForExport(amount, currency)}"\n`;
                });
                csvContent += '\n';

                // Add IBD currency breakdown section
                csvContent += '=== IBD CURRENCY BREAKDOWN ===\n';
                csvContent += 'Currency,IBD Total Amount\n';
                supportedCurrencies.forEach(currency => {
                    const amount = globalStats.ibdCurrencyTotals[currency] || 0;
                    csvContent += `${currency},"${formatCurrencyForExport(amount, currency)}"\n`;
                });
                csvContent += '\n';

                // Add WU currency breakdown section
                csvContent += '=== WU CURRENCY BREAKDOWN ===\n';
                csvContent += 'Currency,WU Total Amount\n';
                supportedCurrencies.forEach(currency => {
                    const amount = globalStats.wuCurrencyTotals[currency] || 0;
                    csvContent += `${currency},"${formatCurrencyForExport(amount, currency)}"\n`;
                });
                csvContent += '\n';

                // Add UTF-8 BOM for proper encoding
                const BOM = '\uFEFF';
                const csvContentWithBOM = BOM + csvContent;

                // Create a Blob and download link
                const blob = new Blob([csvContentWithBOM], { type: 'text/csv;charset=utf-8;' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.setAttribute('href', url);
                link.setAttribute('download', 'dashboard_statistics_with_currency_breakdown.csv');
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }

            // Utility function to format currency
            function formatCurrency(amount) {
                return amount.toLocaleString('en-US', {
                    style: 'currency',
                    currency: 'USD',
                    minimumFractionDigits: 2
                });
            }

            // Helper functions for UI states
            function showLoading() {
                if (loadingIndicator) loadingIndicator.classList.remove('hidden');
                if (errorMessage) errorMessage.classList.add('hidden');

                // Reset progress
                if (progressFill) progressFill.style.width = '0%';
                if (progressText) progressText.textContent = '0%';
                if (filesProcessed) filesProcessed.textContent = '0';
                if (currentBatch) currentBatch.textContent = '0';
                if (loadingText) loadingText.textContent = 'Initializing...';
            }

            function hideLoading() {
                if (loadingIndicator) loadingIndicator.classList.add('hidden');
                stopMemoryMonitoring();
            }

            function showError(message) {
                if (errorMessage) errorMessage.textContent = message;
                if (errorMessage) errorMessage.classList.remove('hidden');
                if (dashboardContainer) dashboardContainer.classList.add('hidden');
                stopMemoryMonitoring();
            }

            function resetDashboard() {
                // Stop any existing monitoring
                stopMemoryMonitoring();

                // Reset metrics
                if (totalFilesEl) totalFilesEl.textContent = '0';
                if (document.getElementById('totalMMK')) document.getElementById('totalMMK').textContent = '0';
                if (document.getElementById('totalUSD')) document.getElementById('totalUSD').textContent = '0';
                if (totalTransactionsEl) totalTransactionsEl.textContent = '0';
                if (totalAmountEl) totalAmountEl.textContent = '0';
                if (avgTransactionEl) avgTransactionEl.textContent = '0';

                // Transaction Summary reset removed as requested

                // Reset serial statistics
                if (totalSerialCountEl) totalSerialCountEl.textContent = '0';
                if (totalUniqueSerialCountEl) totalUniqueSerialCountEl.textContent = '0';
                if (totalCreditUniqueSerialEl) totalCreditUniqueSerialEl.textContent = '0';
                if (totalDebitUniqueSerialEl) totalDebitUniqueSerialEl.textContent = '0';
                
                // Reset HOC analytics
                if (document.getElementById('hocTotalCustomerCount')) document.getElementById('hocTotalCustomerCount').textContent = '0';
                if (document.getElementById('hocTotalAmount')) document.getElementById('hocTotalAmount').textContent = '0';
                if (document.getElementById('hocTotalTransactionCount')) document.getElementById('hocTotalTransactionCount').textContent = '0';
                if (document.getElementById('hocDebitCustomerCount')) document.getElementById('hocDebitCustomerCount').textContent = '0';
                if (document.getElementById('hocDebitTransactionCount')) document.getElementById('hocDebitTransactionCount').textContent = '0';
                if (document.getElementById('hocDebitTotalAmount')) document.getElementById('hocDebitTotalAmount').textContent = '0';
                if (document.getElementById('hocCreditCustomerCount')) document.getElementById('hocCreditCustomerCount').textContent = '0';
                if (document.getElementById('hocCreditTransactionCount')) document.getElementById('hocCreditTransactionCount').textContent = '0';
                if (document.getElementById('hocCreditTotalAmount')) document.getElementById('hocCreditTotalAmount').textContent = '0';
                
                // Reset IBD analytics
                if (document.getElementById('ibdTotalCustomerCount')) document.getElementById('ibdTotalCustomerCount').textContent = '0';
                if (document.getElementById('ibdTotalAmount')) document.getElementById('ibdTotalAmount').textContent = '0';
                if (document.getElementById('ibdTotalTransactionCount')) document.getElementById('ibdTotalTransactionCount').textContent = '0';
                if (document.getElementById('ibdDebitCustomerCount')) document.getElementById('ibdDebitCustomerCount').textContent = '0';
                if (document.getElementById('ibdDebitTransactionCount')) document.getElementById('ibdDebitTransactionCount').textContent = '0';
                if (document.getElementById('ibdDebitTotalAmount')) document.getElementById('ibdDebitTotalAmount').textContent = '0';
                if (document.getElementById('ibdCreditCustomerCount')) document.getElementById('ibdCreditCustomerCount').textContent = '0';
                if (document.getElementById('ibdCreditTransactionCount')) document.getElementById('ibdCreditTransactionCount').textContent = '0';
                if (document.getElementById('ibdCreditTotalAmount')) document.getElementById('ibdCreditTotalAmount').textContent = '0';
                
                // Reset WU analytics
                if (document.getElementById('wuTotalCustomerCount')) document.getElementById('wuTotalCustomerCount').textContent = '0';
                if (document.getElementById('wuTotalAmount')) document.getElementById('wuTotalAmount').textContent = '0';
                if (document.getElementById('wuTotalTransactionCount')) document.getElementById('wuTotalTransactionCount').textContent = '0';
                if (document.getElementById('wuDebitCustomerCount')) document.getElementById('wuDebitCustomerCount').textContent = '0';
                if (document.getElementById('wuDebitTransactionCount')) document.getElementById('wuDebitTransactionCount').textContent = '0';
                if (document.getElementById('wuDebitTotalAmount')) document.getElementById('wuDebitTotalAmount').textContent = '0';
                if (document.getElementById('wuCreditCustomerCount')) document.getElementById('wuCreditCustomerCount').textContent = '0';
                if (document.getElementById('wuCreditTransactionCount')) document.getElementById('wuCreditTransactionCount').textContent = '0';
                if (document.getElementById('wuCreditTotalAmount')) document.getElementById('wuCreditTotalAmount').textContent = '0';
                
                // Reset high-value customer analytics
                if (document.getElementById('highValueMMKCount')) document.getElementById('highValueMMKCount').textContent = '0';
                if (document.getElementById('highValueMMKTotal')) document.getElementById('highValueMMKTotal').textContent = '0';
                if (document.getElementById('highValueUSDCount')) document.getElementById('highValueUSDCount').textContent = '0';
                if (document.getElementById('highValueUSDTotal')) document.getElementById('highValueUSDTotal').textContent = '0';
                if (document.getElementById('uniqueHighValueMMKCustomers')) document.getElementById('uniqueHighValueMMKCustomers').textContent = '0';
                if (document.getElementById('uniqueHighValueUSDCustomers')) document.getElementById('uniqueHighValueUSDCustomers').textContent = '0';

                // Reset progress and memory displays
                if (progressFill) progressFill.style.width = '0%';
                if (progressText) progressText.textContent = '0%';
                if (filesProcessed) filesProcessed.textContent = '0';
                if (currentBatch) currentBatch.textContent = '0';
                if (memoryUsage) memoryUsage.textContent = '0 MB';
                if (memoryUsage) memoryUsage.style.color = '#27ae60';
                if (loadingText) loadingText.textContent = 'Processing data...';

                // Reset file list and table
                // filesListEl.innerHTML = ''; // This element is removed
                if (headerRow) headerRow.innerHTML = '';
                if (previewBody) previewBody.innerHTML = '';

                // Hide dashboard
                if (dashboardContainer) dashboardContainer.classList.add('hidden');
                if (errorMessage) errorMessage.classList.add('hidden');

                // Reset data and force cleanup
                globalStats = {
                    totalFiles: 0,
                    totalTransactions: 0,
                    totalAmount: 0,
                    totalMMK: 0,
                    totalUSD: 0,
                    totalCNY: 0,
                    totalEUR: 0,
                    totalINR: 0,
                    totalJPY: 0,
                    totalSGD: 0,
                    totalTHB: 0,
                    currencyTotals: {
                        MMK: 0,
                        USD: 0,
                        CNY: 0,
                        EUR: 0,
                        INR: 0,
                        JPY: 0,
                        SGD: 0,
                        THB: 0
                    },
                    hocCurrencyTotals: {
                        MMK: 0,
                        USD: 0,
                        CNY: 0,
                        EUR: 0,
                        INR: 0,
                        JPY: 0,
                        SGD: 0,
                        THB: 0
                    },
                    ibdCurrencyTotals: {
                        MMK: 0,
                        USD: 0,
                        CNY: 0,
                        EUR: 0,
                        INR: 0,
                        JPY: 0,
                        SGD: 0,
                        THB: 0
                    },
                    wuCurrencyTotals: {
                        MMK: 0,
                        USD: 0,
                        CNY: 0,
                        EUR: 0,
                        INR: 0,
                        JPY: 0,
                        SGD: 0,
                        THB: 0
                    },
                    highestAmount: 0,
                    lowestAmount: Infinity,
                    highValueMMKCount: 0,
                    highValueMMKTotal: 0,
                    highValueUSDCount: 0,
                    highValueUSDTotal: 0,
                    highValueMMKCustomers: new Set(),
                    highValueUSDCustomers: new Set(),
                    transactionTypes: {},
                    paymentMethods: {},
                    currencies: new Set(),
                    dates: [],
                    allSerialNumbers: [],
                    uniqueSerialNumbers: new Set(),
                    creditSerialNumbers: new Set(),
                    debitSerialNumbers: new Set(),
                    // HOC analytics
                    hocTotalTransactionCount: 0,
                    hocTotalAmount: 0,
                    hocCustomers: new Set(),
                    hocDebitTransactionCount: 0,
                    hocDebitTotalAmount: 0,
                    hocDebitCustomers: new Set(),
                    hocCreditTransactionCount: 0,
                    hocCreditTotalAmount: 0,
                    hocCreditCustomers: new Set(),
                    // IBD analytics
                    ibdTotalTransactionCount: 0,
                    ibdTotalAmount: 0,
                    ibdCustomers: new Set(),
                    ibdDebitTransactionCount: 0,
                    ibdDebitTotalAmount: 0,
                    ibdDebitCustomers: new Set(),
                    ibdCreditTransactionCount: 0,
                    ibdCreditTotalAmount: 0,
                    ibdCreditCustomers: new Set(),
                    // WU analytics
                    wuTotalTransactionCount: 0,
                    wuTotalAmount: 0,
                    wuCustomers: new Set(),
                    wuDebitTransactionCount: 0,
                    wuDebitTotalAmount: 0,
                    wuDebitCustomers: new Set(),
                    wuCreditTransactionCount: 0,
                    wuCreditTotalAmount: 0,
                    wuCreditCustomers: new Set(),
                    sampleData: []
                };
                fileStats = [];

                // Reset currency totals display
                updateCurrencyTotalsDisplay();
                updateHOCCurrencyTotalsDisplay();
                updateIBDCurrencyTotalsDisplay();
                updateWUCurrencyTotalsDisplay();

                forceGarbageCollection();
            }

            // Currency Totals Expandable Component Functions
            function toggleCurrencyTotals() {
                const content = document.getElementById('currencyTotalsContent');
                const icon = document.getElementById('currencyToggleIcon');

                if (content.classList.contains('hidden')) {
                    content.classList.remove('hidden');
                    icon.classList.add('expanded');
                    icon.textContent = '▲';
                } else {
                    content.classList.add('hidden');
                    icon.classList.remove('expanded');
                    icon.textContent = '▼';
                }
            }

            // HOC Currency Totals Toggle Function
            function toggleHOCCurrencyTotals() {
                const content = document.getElementById('hocCurrencyTotalsContent');
                const icon = document.getElementById('hocCurrencyToggleIcon');

                if (content.classList.contains('hidden')) {
                    content.classList.remove('hidden');
                    icon.classList.add('expanded');
                    icon.textContent = '▲';
                } else {
                    content.classList.add('hidden');
                    icon.classList.remove('expanded');
                    icon.textContent = '▼';
                }
            }

            // IBD Currency Totals Toggle Function
            function toggleIBDCurrencyTotals() {
                const content = document.getElementById('ibdCurrencyTotalsContent');
                const icon = document.getElementById('ibdCurrencyToggleIcon');

                if (content.classList.contains('hidden')) {
                    content.classList.remove('hidden');
                    icon.classList.add('expanded');
                    icon.textContent = '▲';
                } else {
                    content.classList.add('hidden');
                    icon.classList.remove('expanded');
                    icon.textContent = '▼';
                }
            }

            // WU Currency Totals Toggle Function
            function toggleWUCurrencyTotals() {
                const content = document.getElementById('wuCurrencyTotalsContent');
                const icon = document.getElementById('wuCurrencyToggleIcon');

                if (content.classList.contains('hidden')) {
                    content.classList.remove('hidden');
                    icon.classList.add('expanded');
                    icon.textContent = '▲';
                } else {
                    content.classList.add('hidden');
                    icon.classList.remove('expanded');
                    icon.textContent = '▼';
                }
            }

            function updateCurrencyTotalsDisplay() {
                const tableBody = document.getElementById('currencyTotalsTableBody');
                if (!tableBody) return;

                // Clear existing rows
                tableBody.innerHTML = '';

                // Define the supported currencies
                const supportedCurrencies = ['MMK', 'USD', 'CNY', 'EUR', 'INR', 'JPY', 'SGD', 'THB'];

                // Create rows for each currency
                supportedCurrencies.forEach(currency => {
                    const amount = globalStats.currencyTotals[currency] || 0;

                    // Only show currencies that have transactions or show all with zero values
                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="currency-code">${currency}</td>
                        <td class="currency-amount">${formatCurrencyAmount(amount, currency)}</td>
                    `;
                    tableBody.appendChild(row);
                });
            }

            function formatCurrencyAmount(amount, currencyCode) {
                // Currency-specific formatting rules
                const currencyFormats = {
                    'MMK': { decimals: 2, symbol: 'MMK' },
                    'USD': { decimals: 2, symbol: '$' },
                    'CNY': { decimals: 2, symbol: '¥' },
                    'EUR': { decimals: 2, symbol: '€' },
                    'INR': { decimals: 2, symbol: '₹' },
                    'JPY': { decimals: 0, symbol: '¥' },
                    'SGD': { decimals: 2, symbol: 'S$' },
                    'THB': { decimals: 2, symbol: '฿' }
                };

                const format = currencyFormats[currencyCode] || { decimals: 2, symbol: currencyCode };

                const options = {
                    minimumFractionDigits: format.decimals,
                    maximumFractionDigits: format.decimals
                };

                const formattedNumber = amount.toLocaleString('en-US', options);

                // For USD, EUR, CNY, INR, JPY, SGD, THB - put symbol before number
                // For MMK - put symbol after number
                if (currencyCode === 'MMK') {
                    return `${formattedNumber} ${format.symbol}`;
                } else {
                    return `${format.symbol}${formattedNumber}`;
                }
            }

            // HOC Currency Totals Display Update
            function updateHOCCurrencyTotalsDisplay() {
                const tableBody = document.getElementById('hocCurrencyTotalsTableBody');
                if (!tableBody) return;

                // Clear existing rows
                tableBody.innerHTML = '';

                // Define the supported currencies
                const supportedCurrencies = ['MMK', 'USD', 'CNY', 'EUR', 'INR', 'JPY', 'SGD', 'THB'];

                // Create rows for each currency
                supportedCurrencies.forEach(currency => {
                    const amount = globalStats.hocCurrencyTotals[currency] || 0;

                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="currency-code">${currency}</td>
                        <td class="currency-amount">${formatCurrencyAmount(amount, currency)}</td>
                    `;
                    tableBody.appendChild(row);
                });
            }

            // IBD Currency Totals Display Update
            function updateIBDCurrencyTotalsDisplay() {
                const tableBody = document.getElementById('ibdCurrencyTotalsTableBody');
                if (!tableBody) return;

                // Clear existing rows
                tableBody.innerHTML = '';

                // Define the supported currencies
                const supportedCurrencies = ['MMK', 'USD', 'CNY', 'EUR', 'INR', 'JPY', 'SGD', 'THB'];

                // Create rows for each currency
                supportedCurrencies.forEach(currency => {
                    const amount = globalStats.ibdCurrencyTotals[currency] || 0;

                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="currency-code">${currency}</td>
                        <td class="currency-amount">${formatCurrencyAmount(amount, currency)}</td>
                    `;
                    tableBody.appendChild(row);
                });
            }

            // WU Currency Totals Display Update
            function updateWUCurrencyTotalsDisplay() {
                const tableBody = document.getElementById('wuCurrencyTotalsTableBody');
                if (!tableBody) return;

                // Clear existing rows
                tableBody.innerHTML = '';

                // Define the supported currencies
                const supportedCurrencies = ['MMK', 'USD', 'CNY', 'EUR', 'INR', 'JPY', 'SGD', 'THB'];

                // Create rows for each currency
                supportedCurrencies.forEach(currency => {
                    const amount = globalStats.wuCurrencyTotals[currency] || 0;

                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td class="currency-code">${currency}</td>
                        <td class="currency-amount">${formatCurrencyAmount(amount, currency)}</td>
                    `;
                    tableBody.appendChild(row);
                });
            }

            // Make functions globally available
            window.toggleCurrencyTotals = toggleCurrencyTotals;
            window.toggleHOCCurrencyTotals = toggleHOCCurrencyTotals;
            window.toggleIBDCurrencyTotals = toggleIBDCurrencyTotals;
            window.toggleWUCurrencyTotals = toggleWUCurrencyTotals;
            window.updateCurrencyTotalsDisplay = updateCurrencyTotalsDisplay;
            window.updateHOCCurrencyTotalsDisplay = updateHOCCurrencyTotalsDisplay;
            window.updateIBDCurrencyTotalsDisplay = updateIBDCurrencyTotalsDisplay;
            window.updateWUCurrencyTotalsDisplay = updateWUCurrencyTotalsDisplay;
            window.formatCurrencyAmount = formatCurrencyAmount;

            console.log('Transaction Dashboard ready!');
        });
    </script>
</body>

</html>
