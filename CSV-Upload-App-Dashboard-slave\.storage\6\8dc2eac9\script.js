document.addEventListener('DOMContentLoaded', function() {
    // DOM Elements
    const csvFileUpload = document.getElementById('csvFileUpload');
    const uploadBtn = document.getElementById('uploadBtn');
    const loadingIndicator = document.getElementById('loadingIndicator');
    const errorMessage = document.getElementById('errorMessage');
    const dashboardContainer = document.getElementById('dashboardContainer');
    const totalTransactionsEl = document.getElementById('totalTransactions');
    const totalAmountEl = document.getElementById('totalAmount');
    const avgTransactionEl = document.getElementById('avgTransaction');
    const headerRow = document.getElementById('headerRow');
    const previewBody = document.getElementById('previewBody');
    
    // Chart objects
    let transactionTypeChart = null;
    let paymentMethodChart = null;
    let amountDistributionChart = null;
    
    // Process CSV file on button click
    uploadBtn.addEventListener('click', function() {
        const file = csvFileUpload.files[0];
        if (!file) {
            showError('Please select a CSV file to upload.');
            return;
        }
        
        if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
            showError('Please upload a valid CSV file.');
            return;
        }
        
        resetDashboard();
        showLoading();
        
        setTimeout(() => {
            processCSV(file);
        }, 500); // Small delay to show loading indicator
    });
    
    // Function to process the CSV file
    function processCSV(file) {
        const reader = new FileReader();
        
        reader.onload = function(event) {
            try {
                const csvData = event.target.result;
                const parsedData = parseCSV(csvData);
                
                if (parsedData.length === 0) {
                    showError('No data found in the CSV file.');
                    return;
                }
                
                analyzeData(parsedData);
                hideLoading();
                dashboardContainer.classList.remove('hidden');
            } catch (error) {
                hideLoading();
                showError('Error processing the CSV file: ' + error.message);
            }
        };
        
        reader.onerror = function() {
            hideLoading();
            showError('Error reading the file.');
        };
        
        reader.readAsText(file);
    }
    
    // Function to parse CSV data
    function parseCSV(csvText) {
        const lines = csvText.split(/\\r?\\n/);
        const result = [];
        
        // Extract header (first line)
        const headers = lines[0].split(',').map(header => header.trim());
        
        // Process data rows
        for (let i = 1; i < lines.length; i++) {
            if (lines[i].trim() === '') continue;
            
            // Handle quoted values with commas inside
            const row = [];
            let inQuotes = false;
            let currentValue = '';
            
            for (let j = 0; j < lines[i].length; j++) {
                const char = lines[i][j];
                
                if (char === '"' && (j === 0 || lines[i][j-1] !== '\\\\')) {
                    inQuotes = !inQuotes;
                } else if (char === ',' && !inQuotes) {
                    row.push(currentValue.trim());
                    currentValue = '';
                } else {
                    currentValue += char;
                }
            }
            
            // Add the last value
            row.push(currentValue.trim());
            
            // Create object from headers and row values
            const rowObject = {};
            for (let j = 0; j < headers.length && j < row.length; j++) {
                rowObject[headers[j]] = row[j].replace(/^"|"$/g, ''); // Remove surrounding quotes
            }
            
            result.push(rowObject);
        }
        
        return result;
    }
    
    // Function to analyze the data and update the dashboard
    function analyzeData(data) {
        // Basic metrics
        const totalTransactions = data.length;
        
        let totalAmount = 0;
        const transactionTypes = {};
        const paymentMethods = {};
        const amountRanges = {
            "0-1000": 0,
            "1001-5000": 0,
            "5001-10000": 0,
            "10001-50000": 0,
            "50001-100000": 0,
            "100001+": 0
        };
        
        // Extract column names from first row
        const columns = Object.keys(data[0]);
        
        // Analyze the data
        data.forEach(transaction => {
            // Calculate total transaction amount
            const amount = parseFloat(transaction.TRANSACTION_AMOUNT) || 0;
            totalAmount += amount;
            
            // Count by transaction type
            const type = transaction.TYPE_OF_BANK_TRANSACTION || 'Unknown';
            transactionTypes[type] = (transactionTypes[type] || 0) + 1;
            
            // Count by payment method
            const method = transaction.PAYMENT_METHOD || 'Unknown';
            paymentMethods[method] = (paymentMethods[method] || 0) + 1;
            
            // Categorize by amount range
            if (amount <= 1000) amountRanges["0-1000"]++;
            else if (amount <= 5000) amountRanges["1001-5000"]++;
            else if (amount <= 10000) amountRanges["5001-10000"]++;
            else if (amount <= 50000) amountRanges["10001-50000"]++;
            else if (amount <= 100000) amountRanges["50001-100000"]++;
            else amountRanges["100001+"]++;
        });
        
        // Calculate average transaction amount
        const avgTransaction = totalAmount / totalTransactions;
        
        // Update dashboard metrics
        totalTransactionsEl.textContent = totalTransactions.toLocaleString();
        totalAmountEl.textContent = formatCurrency(totalAmount);
        avgTransactionEl.textContent = formatCurrency(avgTransaction);
        
        // Create charts
        createTransactionTypeChart(transactionTypes);
        createPaymentMethodChart(paymentMethods);
        createAmountDistributionChart(amountRanges);
        
        // Create data preview table
        createDataPreview(data, columns);
    }
    
    // Function to create transaction type chart
    function createTransactionTypeChart(transactionTypes) {
        const ctx = document.getElementById('transactionTypeChart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (transactionTypeChart) {
            transactionTypeChart.destroy();
        }
        
        const labels = Object.keys(transactionTypes);
        const values = Object.values(transactionTypes);
        
        transactionTypeChart = new Chart(ctx, {
            type: 'pie',
            data: {
                labels: labels,
                datasets: [{
                    data: values,
                    backgroundColor: generateColors(labels.length),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });
    }
    
    // Function to create payment method chart
    function createPaymentMethodChart(paymentMethods) {
        const ctx = document.getElementById('paymentMethodChart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (paymentMethodChart) {
            paymentMethodChart.destroy();
        }
        
        const labels = Object.keys(paymentMethods);
        const values = Object.values(paymentMethods);
        
        paymentMethodChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: values,
                    backgroundColor: generateColors(labels.length),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right',
                    }
                }
            }
        });
    }
    
    // Function to create amount distribution chart
    function createAmountDistributionChart(amountRanges) {
        const ctx = document.getElementById('amountDistributionChart').getContext('2d');
        
        // Destroy existing chart if it exists
        if (amountDistributionChart) {
            amountDistributionChart.destroy();
        }
        
        const labels = Object.keys(amountRanges);
        const values = Object.values(amountRanges);
        
        amountDistributionChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: 'Number of Transactions',
                    data: values,
                    backgroundColor: 'rgba(54, 162, 235, 0.5)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: 'Number of Transactions'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: 'Amount Range'
                        }
                    }
                }
            }
        });
    }
    
    // Function to create data preview table
    function createDataPreview(data, columns) {
        // Clear existing header and rows
        headerRow.innerHTML = '';
        previewBody.innerHTML = '';
        
        // Add headers
        columns.forEach(column => {
            const th = document.createElement('th');
            th.textContent = column;
            headerRow.appendChild(th);
        });
        
        // Add data rows (first 10 only)
        const previewData = data.slice(0, 10);
        previewData.forEach(row => {
            const tr = document.createElement('tr');
            columns.forEach(column => {
                const td = document.createElement('td');
                td.textContent = row[column] || '';
                tr.appendChild(td);
            });
            previewBody.appendChild(tr);
        });
    }
    
    // Utility function to format currency
    function formatCurrency(amount) {
        return amount.toLocaleString('en-US', {
            style: 'currency',
            currency: 'USD',
            minimumFractionDigits: 2
        });
    }
    
    // Utility function to generate colors for charts
    function generateColors(count) {
        const baseColors = [
            'rgba(54, 162, 235, 0.8)',
            'rgba(255, 99, 132, 0.8)',
            'rgba(255, 206, 86, 0.8)',
            'rgba(75, 192, 192, 0.8)',
            'rgba(153, 102, 255, 0.8)',
            'rgba(255, 159, 64, 0.8)',
            'rgba(199, 199, 199, 0.8)',
            'rgba(83, 102, 255, 0.8)',
            'rgba(40, 159, 64, 0.8)',
            'rgba(210, 105, 30, 0.8)'
        ];
        
        const colors = [];
        for (let i = 0; i < count; i++) {
            colors.push(baseColors[i % baseColors.length]);
        }
        
        return colors;
    }
    
    // Helper functions for UI states
    function showLoading() {
        loadingIndicator.classList.remove('hidden');
        errorMessage.classList.add('hidden');
    }
    
    function hideLoading() {
        loadingIndicator.classList.add('hidden');
    }
    
    function showError(message) {
        errorMessage.textContent = message;
        errorMessage.classList.remove('hidden');
        dashboardContainer.classList.add('hidden');
    }
    
    function resetDashboard() {
        // Reset charts
        if (transactionTypeChart) transactionTypeChart.destroy();
        if (paymentMethodChart) paymentMethodChart.destroy();
        if (amountDistributionChart) amountDistributionChart.destroy();
        
        // Reset metrics
        totalTransactionsEl.textContent = '0';
        totalAmountEl.textContent = '0';
        avgTransactionEl.textContent = '0';
        
        // Reset table
        headerRow.innerHTML = '';
        previewBody.innerHTML = '';
        
        // Hide dashboard
        dashboardContainer.classList.add('hidden');
        errorMessage.classList.add('hidden');
    }
    
    console.log('Transaction Dashboard ready!');
});