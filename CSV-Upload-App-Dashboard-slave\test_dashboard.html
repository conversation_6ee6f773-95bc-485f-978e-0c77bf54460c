<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dashboard</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-section {
            background: #f5f5f5;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .hidden {
            display: none;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #e0e0e0;
            border-radius: 10px;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: #007bff;
            border-radius: 10px;
            width: 0%;
            transition: width 0.3s;
        }
    </style>
</head>
<body>
    <h1>CSV Upload Test Dashboard</h1>
    
    <div class="upload-section">
        <p>Select CSV files to test processing:</p>
        <input type="file" id="csvFileUpload" accept=".csv" multiple />
        <button id="uploadBtn">Process Files</button>
    </div>
    
    <div id="errorMessage" class="error hidden"></div>
    <div id="successMessage" class="success hidden"></div>
    
    <div id="progressContainer" class="hidden">
        <div class="progress">
            <div id="progressFill" class="progress-fill"></div>
        </div>
        <div id="progressText">0%</div>
    </div>
    
    <div id="results" class="hidden">
        <h3>Results:</h3>
        <div id="fileCount">Files processed: 0</div>
        <div id="recordCount">Total records: 0</div>
    </div>

    <script>
        console.log('Script loading...');
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing...');
            
            // Get elements
            const csvFileUpload = document.getElementById('csvFileUpload');
            const uploadBtn = document.getElementById('uploadBtn');
            const errorMessage = document.getElementById('errorMessage');
            const successMessage = document.getElementById('successMessage');
            const progressContainer = document.getElementById('progressContainer');
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            const results = document.getElementById('results');
            const fileCount = document.getElementById('fileCount');
            const recordCount = document.getElementById('recordCount');
            
            console.log('Elements found:', {
                csvFileUpload: !!csvFileUpload,
                uploadBtn: !!uploadBtn,
                errorMessage: !!errorMessage
            });
            
            // Test button click
            uploadBtn.addEventListener('click', function() {
                console.log('Button clicked!');
                
                const files = csvFileUpload.files;
                console.log('Files selected:', files.length);
                
                if (files.length === 0) {
                    showError('Please select at least one CSV file.');
                    return;
                }
                
                hideMessages();
                showProgress();
                processFiles(files);
            });
            
            function showError(message) {
                console.log('Showing error:', message);
                errorMessage.textContent = message;
                errorMessage.classList.remove('hidden');
                successMessage.classList.add('hidden');
            }
            
            function showSuccess(message) {
                console.log('Showing success:', message);
                successMessage.textContent = message;
                successMessage.classList.remove('hidden');
                errorMessage.classList.add('hidden');
            }
            
            function hideMessages() {
                errorMessage.classList.add('hidden');
                successMessage.classList.add('hidden');
            }
            
            function showProgress() {
                progressContainer.classList.remove('hidden');
                progressFill.style.width = '0%';
                progressText.textContent = '0%';
            }
            
            function updateProgress(current, total) {
                const percent = Math.round((current / total) * 100);
                progressFill.style.width = percent + '%';
                progressText.textContent = percent + '%';
            }
            
            async function processFiles(files) {
                console.log('Processing', files.length, 'files...');
                let totalRecords = 0;
                
                try {
                    for (let i = 0; i < files.length; i++) {
                        const file = files[i];
                        console.log('Processing file:', file.name);
                        
                        updateProgress(i, files.length);
                        
                        const records = await processFile(file);
                        totalRecords += records;
                        
                        console.log('File processed:', file.name, 'Records:', records);
                    }
                    
                    updateProgress(files.length, files.length);
                    
                    // Show results
                    fileCount.textContent = `Files processed: ${files.length}`;
                    recordCount.textContent = `Total records: ${totalRecords}`;
                    results.classList.remove('hidden');
                    progressContainer.classList.add('hidden');
                    
                    showSuccess(`Successfully processed ${files.length} files with ${totalRecords} total records!`);
                    
                } catch (error) {
                    console.error('Processing error:', error);
                    progressContainer.classList.add('hidden');
                    showError('Error processing files: ' + error.message);
                }
            }
            
            function processFile(file) {
                return new Promise((resolve, reject) => {
                    console.log('Reading file:', file.name);
                    
                    const reader = new FileReader();
                    
                    reader.onload = function(event) {
                        try {
                            const csvData = event.target.result;
                            console.log('File read, size:', csvData.length);
                            
                            // Simple CSV parsing - count lines
                            const lines = csvData.split('\n');
                            const recordCount = Math.max(0, lines.length - 1); // Subtract header
                            
                            console.log('Lines found:', lines.length, 'Records:', recordCount);
                            resolve(recordCount);
                            
                        } catch (error) {
                            console.error('Parse error:', error);
                            reject(error);
                        }
                    };
                    
                    reader.onerror = function() {
                        const error = new Error(`Error reading file: ${file.name}`);
                        console.error('Read error:', error);
                        reject(error);
                    };
                    
                    reader.readAsText(file);
                });
            }
            
            console.log('Dashboard initialized successfully!');
        });
        
        console.log('Script loaded!');
    </script>
</body>
</html>
