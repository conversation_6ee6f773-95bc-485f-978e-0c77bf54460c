(function(){const m=document.createElement("link").relList;if(m&&m.supports&&m.supports("modulepreload"))return;for(const s of document.querySelectorAll('link[rel="modulepreload"]'))g(s);new MutationObserver(s=>{for(const i of s)if(i.type==="childList")for(const h of i.addedNodes)h.tagName==="LINK"&&h.rel==="modulepreload"&&g(h)}).observe(document,{childList:!0,subtree:!0});function E(s){const i={};return s.integrity&&(i.integrity=s.integrity),s.referrerPolicy&&(i.referrerPolicy=s.referrerPolicy),s.crossOrigin==="use-credentials"?i.credentials="include":s.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function g(s){if(s.ep)return;s.ep=!0;const i=E(s);fetch(s.href,i)}})();document.addEventListener("DOMContentLoaded",function(){const x=document.getElementById("csvFileUpload"),m=document.getElementById("uploadBtn"),E=document.getElementById("loadingIndicator"),g=document.getElementById("errorMessage"),s=document.getElementById("dashboardContainer"),i=document.getElementById("totalTransactions"),h=document.getElementById("totalAmount"),O=document.getElementById("avgTransaction"),T=document.getElementById("headerRow"),w=document.getElementById("previewBody");let p=null,y=null,b=null;m.addEventListener("click",function(){const e=x.files[0];if(!e){C("Please select a CSV file to upload.");return}if(e.type!=="text/csv"&&!e.name.endsWith(".csv")){C("Please upload a valid CSV file.");return}F(),j(),setTimeout(()=>{I(e)},500)});function I(e){const n=new FileReader;n.onload=function(o){try{const t=o.target.result,r=S(t);if(r.length===0){C("No data found in the CSV file.");return}M(r),L(),s.classList.remove("hidden")}catch(t){L(),C("Error processing the CSV file: "+t.message)}},n.onerror=function(){L(),C("Error reading the file.")},n.readAsText(e)}function S(e){console.log("CSV Text length:",e.length),console.log("First 500 characters:",e.substring(0,500));const n=e.split(/\r?\n/);console.log("Total lines:",n.length);const o=[];if(n.length<2)throw new Error("CSV file must contain at least a header row and one data row.");let t=",";n[0].includes("	")&&(t="	");const r=n[0].split(t).map(l=>l.trim().replace(/^"|"$/g,""));if(console.log("Headers found:",r.length,r.slice(0,5)),r.length===0)throw new Error("No headers found in CSV file.");let a=0;for(let l=1;l<n.length;l++){const f=n[l].trim();if(f==="")continue;let d;if(f.includes('"')?d=D(f,t):d=f.split(t).map(c=>c.trim()),d.length===0)continue;const u={};for(let c=0;c<r.length;c++){const v=c<d.length?d[c].replace(/^"|"$/g,"").trim():"";u[r[c]]=v}o.push(u),a++}return console.log("Valid rows parsed:",a),o}function D(e,n=","){const o=[];let t=!1,r="";for(let a=0;a<e.length;a++){const l=e[a],f=a<e.length-1?e[a+1]:null;l==='"'?t&&f==='"'?(r+='"',a++):t=!t:l===n&&!t?(o.push(r.trim()),r=""):r+=l}return o.push(r.trim()),o}function M(e){const n=e.length;let o=0;const t={},r={},a={"0-1000":0,"1001-5000":0,"5001-10000":0,"10001-50000":0,"50001-100000":0,"100001+":0},l=Object.keys(e[0]);e.forEach(d=>{const u=parseFloat(d.TRANSACTION_AMOUNT)||0;o+=u;const c=d.TYPE_OF_BANK_TRANSACTION||"Unknown";t[c]=(t[c]||0)+1;const v=d.PAYMENT_METHOD||"Unknown";r[v]=(r[v]||0)+1,u<=1e3?a["0-1000"]++:u<=5e3?a["1001-5000"]++:u<=1e4?a["5001-10000"]++:u<=5e4?a["10001-50000"]++:u<=1e5?a["50001-100000"]++:a["100001+"]++});const f=o/n;i.textContent=n.toLocaleString(),h.textContent=A(o),O.textContent=A(f),N(t),V(r),P(a),R(e,l)}function N(e){const n=document.getElementById("transactionTypeChart").getContext("2d");p&&p.destroy();const o=Object.keys(e),t=Object.values(e);p=new Chart(n,{type:"pie",data:{labels:o,datasets:[{data:t,backgroundColor:B(o.length),borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right"}}}})}function V(e){const n=document.getElementById("paymentMethodChart").getContext("2d");y&&y.destroy();const o=Object.keys(e),t=Object.values(e);y=new Chart(n,{type:"doughnut",data:{labels:o,datasets:[{data:t,backgroundColor:B(o.length),borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,plugins:{legend:{position:"right"}}}})}function P(e){const n=document.getElementById("amountDistributionChart").getContext("2d");b&&b.destroy();const o=Object.keys(e),t=Object.values(e);b=new Chart(n,{type:"bar",data:{labels:o,datasets:[{label:"Number of Transactions",data:t,backgroundColor:"rgba(54, 162, 235, 0.5)",borderColor:"rgba(54, 162, 235, 1)",borderWidth:1}]},options:{responsive:!0,maintainAspectRatio:!1,scales:{y:{beginAtZero:!0,title:{display:!0,text:"Number of Transactions"}},x:{title:{display:!0,text:"Amount Range"}}}}})}function R(e,n){T.innerHTML="",w.innerHTML="",n.forEach(t=>{const r=document.createElement("th");r.textContent=t,T.appendChild(r)}),e.slice(0,10).forEach(t=>{const r=document.createElement("tr");n.forEach(a=>{const l=document.createElement("td");l.textContent=t[a]||"",r.appendChild(l)}),w.appendChild(r)})}function A(e){return e.toLocaleString("en-US",{style:"currency",currency:"USD",minimumFractionDigits:2})}function B(e){const n=["rgba(54, 162, 235, 0.8)","rgba(255, 99, 132, 0.8)","rgba(255, 206, 86, 0.8)","rgba(75, 192, 192, 0.8)","rgba(153, 102, 255, 0.8)","rgba(255, 159, 64, 0.8)","rgba(199, 199, 199, 0.8)","rgba(83, 102, 255, 0.8)","rgba(40, 159, 64, 0.8)","rgba(210, 105, 30, 0.8)"],o=[];for(let t=0;t<e;t++)o.push(n[t%n.length]);return o}function j(){E.classList.remove("hidden"),g.classList.add("hidden")}function L(){E.classList.add("hidden")}function C(e){g.textContent=e,g.classList.remove("hidden"),s.classList.add("hidden")}function F(){p&&p.destroy(),y&&y.destroy(),b&&b.destroy(),i.textContent="0",h.textContent="0",O.textContent="0",T.innerHTML="",w.innerHTML="",s.classList.add("hidden"),g.classList.add("hidden")}console.log("Transaction Dashboard ready!")});
